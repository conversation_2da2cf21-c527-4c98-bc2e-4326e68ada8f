package org.pronexus.user.domain.service.impl;

import com.salaryadvance.commonlibrary.utils.TokenUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.pronexus.user.app.dtos.UserPasscodeInfoDto;
import org.pronexus.user.domain.feign.adapter.PortalClient;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Test class for UserPasscodeServiceImpl
 */
@ExtendWith(MockitoExtension.class)
class UserPasscodeServiceImplTest {

    @Mock
    private PortalClient portalClient;

    @InjectMocks
    private UserPasscodeServiceImpl userPasscodeService;

    private static final String TEST_USER_ID = "test-user-id";

    private UserPasscodeInfoDto testPasscodeInfo;

    @BeforeEach
    void setUp() {
        testPasscodeInfo = UserPasscodeInfoDto.builder()
                .userId(TEST_USER_ID)
                .passcodeEnabled(true)
                .passcodeFailedAttempts(0)
                .passcodeExpiryDate(LocalDateTime.now().plusDays(30))
                .passcodeLastResetDate(LocalDateTime.now())
                .build();
    }

    @Test
    void getCurrentUserPasscodeInfo_ShouldReturnPasscodeInfo() {
        // Given
        try (MockedStatic<TokenUtils> tokenUtils = mockStatic(TokenUtils.class)) {
            tokenUtils.when(TokenUtils::getUserId).thenReturn(TEST_USER_ID);
            when(portalClient.getUserPasscodeInfo(TEST_USER_ID)).thenReturn(testPasscodeInfo);

            // When
            UserPasscodeInfoDto result = userPasscodeService.getCurrentUserPasscodeInfo();

            // Then
            assertNotNull(result);
            assertEquals(TEST_USER_ID, result.getUserId());
            assertTrue(result.getPasscodeEnabled());
            assertEquals(0, result.getPasscodeFailedAttempts());
            verify(portalClient).getUserPasscodeInfo(TEST_USER_ID);
        }
    }

    @Test
    void getUserPasscodeInfo_WithValidUserId_ShouldReturnPasscodeInfo() {
        // Given
        when(portalClient.getUserPasscodeInfo(TEST_USER_ID)).thenReturn(testPasscodeInfo);

        // When
        UserPasscodeInfoDto result = userPasscodeService.getUserPasscodeInfo(TEST_USER_ID);

        // Then
        assertNotNull(result);
        assertEquals(TEST_USER_ID, result.getUserId());
        assertTrue(result.getPasscodeEnabled());
        verify(portalClient).getUserPasscodeInfo(TEST_USER_ID);
    }

    @Test
    void getUserPasscodeInfo_WhenPortalReturnsNull_ShouldReturnDefaultInfo() {
        // Given
        when(portalClient.getUserPasscodeInfo(TEST_USER_ID)).thenReturn(null);

        // When
        UserPasscodeInfoDto result = userPasscodeService.getUserPasscodeInfo(TEST_USER_ID);

        // Then
        assertNotNull(result);
        assertEquals(TEST_USER_ID, result.getUserId());
        assertFalse(result.getPasscodeEnabled());
        assertEquals(0, result.getPasscodeFailedAttempts());
        verify(portalClient).getUserPasscodeInfo(TEST_USER_ID);
    }

    @Test
    void getUserPasscodeInfo_WhenExceptionOccurs_ShouldReturnDefaultInfo() {
        // Given
        when(portalClient.getUserPasscodeInfo(TEST_USER_ID))
                .thenThrow(new RuntimeException("Service unavailable"));

        // When
        UserPasscodeInfoDto result = userPasscodeService.getUserPasscodeInfo(TEST_USER_ID);

        // Then
        assertNotNull(result);
        assertEquals(TEST_USER_ID, result.getUserId());
        assertFalse(result.getPasscodeEnabled());
        assertEquals(0, result.getPasscodeFailedAttempts());
        verify(portalClient).getUserPasscodeInfo(TEST_USER_ID);
    }

    @Test
    void passcodeInfoDto_IsLocked_ShouldReturnTrueWhenFailedAttemptsExceedsLimit() {
        // Given
        UserPasscodeInfoDto lockedPasscode = UserPasscodeInfoDto.builder()
                .userId(TEST_USER_ID)
                .passcodeFailedAttempts(3)
                .build();

        // When & Then
        assertTrue(lockedPasscode.isLocked());
    }

    @Test
    void passcodeInfoDto_IsExpired_ShouldReturnTrueWhenExpired() {
        // Given
        UserPasscodeInfoDto expiredPasscode = UserPasscodeInfoDto.builder()
                .userId(TEST_USER_ID)
                .passcodeExpiryDate(LocalDateTime.now().minusDays(1))
                .build();

        // When & Then
        assertTrue(expiredPasscode.isExpired());
    }
}
