package org.pronexus.user.domain.feign.adapter;

import com.nimbusds.oauth2.sdk.GrantType;
import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.exception.base.ExternalServiceException;
import com.salaryadvance.commonlibrary.exception.base.UnauthorizedException;
import com.salaryadvance.commonlibrary.utils.JsonUtils;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.representations.idm.*;
import org.keycloak.representations.idm.authorization.*;
import org.pronexus.user.domain.feign.KeycloakFeignClient;
import org.pronexus.user.domain.feign.dto.PermissionDetailDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class KeycloakClient {

    @Value("${keycloak.master-client-id}")
    private String masterClientId;

    @Value("${keycloak.master-username}")
    private String masterUsername;

    @Value("${keycloak.master-password}")
    private String masterPassword;

    @Value("${keycloak.admin.username}")
    private String systemAdminUsername;

    @Value("${keycloak.admin.password}")
    private String systemAdminPassword;

    @Value("${keycloak.client-id}")
    private String clientId;

    @Value("${keycloak.client-secret}")
    private String clientSecret;

    private final KeycloakFeignClient keycloakFeignClient;

    //=========== Common api ===========//

    /**
     * Get master token for key cloak admin api call
     * @throws RuntimeException if error occur
     * */
    public String getMasterToken() {
        Map<String, ?> request = Map.of(
                "grant_type", GrantType.PASSWORD.getValue(),
                "client_id", masterClientId,
                "username", masterUsername,
                "password", masterPassword
        );

        try {
            AccessTokenResponse response = keycloakFeignClient.getMasterToken(request);
            return response.getToken();
        } catch (FeignException e) {
            log.error("Cannot get master token: {}", e.getMessage());
            throw new UnauthorizedException("Can not authorize user", e.getMessage());
        }
    }


    /**
     * Get master token for key cloak admin api call
     * @throws ExternalServiceException if error occur
     * */
    public String getClientToken() {
        Map<String, ?> request = Map.of(
                "grant_type", GrantType.CLIENT_CREDENTIALS.getValue(),
                "client_id", clientId,
                "client_secret", clientSecret
        );

        try {
            AccessTokenResponse response = keycloakFeignClient.getToken(request);
            return response.getToken();
        } catch (FeignException e) {
            log.error("Cannot get client token: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get client token", e.getMessage());
        }
    }


    /**
     * Get master token for key cloak admin api call
     * @throws ExternalServiceException if error occur
     * */
    public String getSystemAdminToken() {
        return getToken(masterUsername, masterPassword).getToken();
    }


    /**
     * Get token of user
     * @throws RuntimeException if error occur
     * */
    public AccessTokenResponse getToken(String username, String password) {
        Map<String, ?> request = Map.of(
                "grant_type", GrantType.PASSWORD.getValue(),
                "client_id", clientId,
                "client_secret", clientSecret,
                "username", username,
                "password", password
        );

        try {
            return keycloakFeignClient.getToken(request);
        } catch (FeignException e) {
            log.error("Cannot get user token: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get user token", e.getMessage());
        }
    }

    /**
     * Refresh token for user
     * @throws RuntimeException if error occur
     * */
    public AccessTokenResponse refreshToken(String refreshToken) {
        Map<String, ?> request = Map.of(
                "grant_type", GrantType.REFRESH_TOKEN.getValue(),
                "client_id", clientId,
                "client_secret", clientSecret,
                "refresh_token", refreshToken
        );

        try {
            return keycloakFeignClient.getToken(request);
        } catch (FeignException e) {
            log.error("Cannot refresh user token: {}", e.getMessage());
            throw new ExternalServiceException("Cannot refresh user token", e.getMessage());
        }
    }


    /**
     * Get client info
     * @throws RuntimeException if error occur
     * */
    public ClientRepresentation getClientInfo() {
        String token = "Bearer " + getClientToken();
        try {
            return keycloakFeignClient.getClients(token, clientId).getFirst();
        } catch (FeignException e) {
            log.error("Cannot get client info: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get client info", e.getMessage());
        }
    }


    //=========== Resource api ===========//


    /**
     * Get client all resource
     * @throws ExternalServiceException if error occur
     * */
    public List<ResourceRepresentation> getResources() {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.getResources(token, clientUuid);
        } catch (FeignException e) {
            log.error("Cannot get resource list: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get resource list", e.getMessage());
        }
    }


    /**
     * Get client all resource filter by role
     * @return
     */
    public List<ResourceRepresentation> getResourcesSet() {
        String token = getClientToken();
        try {
            return keycloakFeignClient.getResourcesSet(token, true);
        } catch (FeignException e) {
            log.error("Cannot get resource list: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get resource list", e.getMessage());
        }
    }


    /**
     * Get resource detail
     * @param resourceId resource id
     * @throws ExternalServiceException if error occur
     * */
    public ResourceRepresentation getResource(String resourceId) {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.getResource(token, resourceId, clientUuid);
        } catch (FeignException e) {
            log.error("Cannot get resource detail: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get resource detail", e.getMessage());
        }
    }


    /**
     * Create a resource
     * @param data creation data
     * @throws ExternalServiceException if error occur
     * */
    public ResourceRepresentation createResource(ResourceRepresentation data) {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.createResource(token, clientUuid, data);
        } catch (FeignException e) {
            log.error("Cannot create resource: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot create resource!", e.getMessage());
        }
    }


    /**
     * Update existing resource
     * @param resourceId id of resource
     * @param data update data
     * @throws ExternalServiceException if error occur
     * */
    public ResourceRepresentation updateResource(String resourceId, ResourceRepresentation data) {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.updateResource(token, clientUuid, resourceId, data);
        } catch (FeignException e) {
            log.error("Cannot update resource: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot update resource!", Map.of("details", e.getMessage()));
        }
    }


    /**
     * Update existing resource
     * @param resourceId id of resource
     * @throws ExternalServiceException if error occur
     * */
    public void deleteResource(String resourceId) {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            keycloakFeignClient.deleteResource(token, clientUuid, resourceId);
        } catch (FeignException e) {
            log.error("Cannot delete resource: {}, details: {}", resourceId, e.getMessage());
            throw new ExternalServiceException("Cannot delete resource", e.getMessage());
        }
    }


    public PolicyEvaluationResponse evaluate() {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        String userId = TokenUtils.getUserId();
        try {
            PolicyEvaluationRequest evaluationRequest = new PolicyEvaluationRequest();
            evaluationRequest.setUserId(userId);
            evaluationRequest.setEntitlements(false);
            return keycloakFeignClient.evaluate(token, clientUuid, evaluationRequest).getBody();
        } catch (FeignException e) {
            log.error("Cannot evaluate user permissions: {}", e.getMessage());
            throw new ExternalServiceException("Cannot evaluate user permissions!", e.getMessage());
        }
    }


    //=========== Role api ===========//


    /**
     * Get all role
     * @throws ExternalServiceException if error occur
     * */
    public List<RoleRepresentation> getRoles() {
        String token = "Bearer " + getClientToken();
        try {
            return keycloakFeignClient.getRoles(token);
        } catch (FeignException e) {
            log.error("Cannot get roles: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get role list", e.getMessage());
        }
    }


    /**
     * Get role detail
     * @param roleName nam of role
     * @throws ExternalServiceException if error occur
     * */
    public RoleRepresentation getRole(String roleName) {
        String token = "Bearer " + getClientToken();
        try {
            return keycloakFeignClient.getRole(token, roleName);
        } catch (FeignException e) {
            log.error("Cannot get role: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get role", e.getMessage());
        }
    }


    /**
     * Create a role
     * @throws ExternalServiceException if error occur
     * */
    public RoleRepresentation createRole(RoleRepresentation data) {
        String token = "Bearer " + getClientToken();
        try {
            return keycloakFeignClient.createRole(token, data);
        } catch (FeignException e) {
            log.error("Cannot create role: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot create role", e.getMessage());
        }
    }


    /**
     * Update existing role
     * @param roleName role name
     * @param data update data
     * @throws ExternalServiceException if error occur
     * */
    public RoleRepresentation updateRole(String roleName, RoleRepresentation data) {
        String token = "Bearer " + getClientToken();
        try {
            return keycloakFeignClient.updateRole(token, roleName, data);
        } catch (FeignException e) {
            log.error("Cannot update role: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot update role", e.getMessage());
        }
    }


    /**
     * Delete existing role
     * @param roleName role name
     * @throws ExternalServiceException if error occur
     * */
    public void deleteRole(String roleName) {
        String token = "Bearer " + getClientToken();
        try {
            keycloakFeignClient.deleteRole(token, roleName);
        } catch (FeignException e) {
            log.error("Cannot delete role: {}, details: {}", roleName, e.getMessage());
            throw new ExternalServiceException("Cannot delete role", Map.of("details", e.getMessage()));
        }
    }


    //=========== Scope api ===========//


    /**
     * Get all scope
     * @return all available scope in system
     * @throws ExternalServiceException if error occur
     * */
    public List<ScopeRepresentation> getScopes() {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.getScopes(token, clientUuid);
        } catch (FeignException e) {
            log.error("Cannot get scopes: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get scopes", e.getMessage());
        }
    }


    /**
     * Get a scope details
     * @param scopeId scope identifier
     * @throws ExternalServiceException if error occur
     * */
    public ScopeRepresentation getScopeById(String scopeId) {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.getScopeById(token, clientUuid, scopeId);
        } catch (FeignException e) {
            log.error("Cannot get scope detail: {}, details: {}", scopeId, e.getMessage());
            throw new ExternalServiceException("Cannot get scope detail", e.getMessage());
        }
    }


    /**
     * Create a scope
     * @param data scope data
     * @throws ExternalServiceException if error occur
     * */
    public ScopeRepresentation createScope(ScopeRepresentation data) {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.createScope(token, clientUuid, data);
        } catch (FeignException e) {
            log.error("Cannot create scope: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot create scope", e.getMessage());
        }
    }


    /**
     * Update existing scope
     * @param scopeId id of scope
     * @param data update data
     * @throws ExternalServiceException if error occur
     * */
    public ScopeRepresentation updateScope(String scopeId, ScopeRepresentation data) {
        String token = "Bearer " + getMasterToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.updateScope(token, clientUuid, scopeId, data);
        } catch (FeignException e) {
            log.error("Cannot update scope: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot update scope", e.getMessage());
        }
    }


    /**
     * Update existing scope
     * @param scopeId id of scope
     * @throws ExternalServiceException if error occur
     * */
    public void deleteScope(String scopeId) {
        String token = "Bearer " + getMasterToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            keycloakFeignClient.deleteScope(token, clientUuid, scopeId);
        } catch (FeignException e) {
            log.error("Cannot delete scope: {}, details: {}", scopeId, e.getMessage());
            throw new ExternalServiceException("Cannot delete scope!", e.getMessage());
        }
    }


    //=========== Permission api ===========//


    /**
     * Get all permission (with full detail for each permission)
     * @throws ExternalServiceException if error occur
     */
    public List<PermissionDetailDTO> getPermissions() {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            List<AbstractPolicyRepresentation> basicList = keycloakFeignClient.getPermissions(token, clientUuid);
            List<PermissionDetailDTO> detailedList = new ArrayList<>();
            for (AbstractPolicyRepresentation basic : basicList) {
                try {
                    // Lấy lại thông tin chi tiết từng permission qua id (enrich)
                    PermissionDetailDTO detail = getPermissionDetail(basic.getId());
                    detailedList.add(detail);
                } catch (Exception e) {
                    log.warn("Cannot get detail for permission id: {}", basic.getId(), e);
                    // fallback: trả về DTO chỉ chứa permission gốc
                    detailedList.add(new PermissionDetailDTO(basic, new ArrayList<>(), new ArrayList<>()));
                }
            }
            return detailedList;
        } catch (FeignException e) {
            log.error("Cannot get permissions: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get permissions", e.getMessage());
        }
    }


    /**
     * Get permission detail (chỉ trả về object gốc từ Keycloak, không enrich)
     * @param permissionId permission id
     * @throws ExternalServiceException if error occur
     */
    public AbstractPolicyRepresentation getPermission(String permissionId) {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.getPermission(token, permissionId, clientUuid);
        } catch (FeignException e) {
            log.error("Cannot get permission detail: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get permission detail", e.getMessage());
        }
    }


    /**
     * Get permission detail (enrich policies and scopes info, return DTO)
     * @param permissionId permission id
     * @throws ExternalServiceException if error occur
     */
    public PermissionDetailDTO getPermissionDetail(String permissionId) {
        String token = "Bearer " + getMasterToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            AbstractPolicyRepresentation permission = keycloakFeignClient.getPermission(token, permissionId, clientUuid);

            // Enrich policies
            List<AbstractPolicyRepresentation> policiesDetail = new ArrayList<>();
            try {
                List<AbstractPolicyRepresentation> policies = keycloakFeignClient.getAssociatedPoliciesOfPolicy(token, clientUuid, permissionId);
                policiesDetail.addAll(policies);
            } catch (Exception e) {
                log.warn("Cannot enrich policy {} for permission {}", permissionId, e);
            }

            // Enrich scopes
            List<ScopeRepresentation> scopesDetail = new ArrayList<>();
            try {
                List<ScopeRepresentation> scopes = keycloakFeignClient.getScopesOfPolicy(token, clientUuid, permissionId);
                if (scopes != null) scopesDetail.addAll(scopes);
            } catch (Exception e) {
                log.warn("Cannot get scopes of scope-permission {}: {}", permissionId, e.getMessage());
            }

            // Enrich resourcesDetail
            List<ResourceRepresentation> resourcesDetail = new ArrayList<>();
            try {
                List<ResourceRepresentation> resources = getResourcesOfPolicy(token, clientUuid, permissionId);
                if (resources != null) resourcesDetail.addAll(resources);
            } catch (Exception e) {
                log.warn("Cannot get resources of policy {}: {}", permissionId, e.getMessage());
            }
            return new PermissionDetailDTO(permission, policiesDetail, scopesDetail, resourcesDetail);
        } catch (FeignException e) {
            log.error("Cannot get permission detail: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get permission detail", e.getMessage());
        }
    }


    /**
     * Create a scope permission
     * @throws ExternalServiceException if error occur
     * */
    public AbstractPolicyRepresentation createScopePermission(AbstractPolicyRepresentation data) {
        String token = "Bearer " + getMasterToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.createScopePermission(token, clientUuid, data);
        } catch (FeignException e) {
            log.error("Cannot create scope permission: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot create scope permission", e.getMessage());
        }
    }


    /**
     * Create a resource permission
     * @throws ExternalServiceException if error occur
     * */
    public AbstractPolicyRepresentation createResourcePermission(AbstractPolicyRepresentation data) {
        String token = "Bearer " + getClientToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.createResourcePermission(token, clientUuid, data);
        } catch (FeignException e) {
            log.error("Cannot create resource permission: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot create resource permission", e.getMessage());
        }
    }


    /**
     * Update existing scope
     * @param permissionId id of scope
     * @param data update data
     * @throws ExternalServiceException if error occur
     * */
    public AbstractPolicyRepresentation updateScopePermission(String permissionId, AbstractPolicyRepresentation data) {
        String token = "Bearer " + getMasterToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.updateScopePermission(token, clientUuid, permissionId, data);
        } catch (FeignException e) {
            log.error("Cannot update scope permission: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot update scope permission", e.getMessage());
        }
    }


    /**
     * Update existing scope
     * @param permissionId id of scope
     * @param data update data
     * @throws ExternalServiceException if error occur
     * */
    public AbstractPolicyRepresentation updateResourcePermission(String permissionId, AbstractPolicyRepresentation data) {
        String token = "Bearer " + getMasterToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.updateResourcePermission(token, clientUuid, permissionId, data);
        } catch (FeignException e) {
            log.error("Cannot update resource permission: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot update scope resource permission", e.getMessage());
        }
    }


    /**
     * Update existing scope
     * @param permissionId id of scope
     * @throws ExternalServiceException if error occur
     * */
    public void deletePermission(String permissionId) {
        String token = "Bearer " + getMasterToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            keycloakFeignClient.deletePermission(token, clientUuid, permissionId);
        } catch (FeignException e) {
            log.error("Cannot delete permission: {}, details: {}", permissionId, e.getMessage());
            throw new ExternalServiceException("Cannot delete permission", e.getMessage());
        }
    }


    public RolePolicyRepresentation createRolePolicy(RolePolicyRepresentation data) {
        String token = "Bearer " + getMasterToken();
        ClientRepresentation client = getClientInfo();
        String clientUuid = client.getId();
        try {
            return keycloakFeignClient.createRolePolicy(token, clientUuid, data);
        } catch (FeignException e) {
            log.error("Cannot create role policy: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot delete role policy", e.getMessage());
        }
    }


    //=========== User api ===========//

    public Page<UserRepresentation> getUsers(Pageable pageable, String username) {
        return getUsers(pageable, username, null);
    }

    public Page<UserRepresentation> getUsers(Pageable pageable, String username, String email) {
        Integer page = pageable.getPageNumber();
        Integer size = pageable.getPageSize();
        Integer first = page * size;
        String token = "Bearer " + getClientToken();
        try {
            List<UserRepresentation> users = keycloakFeignClient.getUsers(token, username, email, first, size);
            Integer total = countUser(username, email);
            return new PageImpl<>(users, pageable, total);
        } catch (FeignException e) {
            log.error("Cannot get users: {}", e.getMessage());
            throw new ExternalServiceException("Cannot get users", e.getMessage());
        }
    }


    /**
     * Count user
     * @param username query username
     * */
    public Integer countUser(String username, String email) {
        String token = "Bearer " + getClientToken();
        try {
            return keycloakFeignClient.countUser(token, username, email);
        } catch (FeignException e) {
            log.error("Cannot count number of user, details: {}", e.getMessage(), e);
            throw new ExternalServiceException("Cannot count number of user!", e.getMessage());
        }
    }


    /**
     * Check if username is existed
     * @param username username
     * */
    public boolean usernameExisted(String username) {
        Integer count = countUser(username, null);
        return count > 0;
    }


    /**
     * Find user
     * @param username username
     * */
    public UserRepresentation findUserByUsername(String username) {
        String token = "Bearer " + getClientToken();
        try {
            return keycloakFeignClient.findUserByUsername(token, username, true).getFirst();
        } catch (FeignException e) {
            log.error("Cannot find user: {}, details: {}", username, e.getMessage());
            throw new RuntimeException("Cannot find user!", e);
        }
    }


    /**
     * Find user
     * @param userId user id
     * */
    public UserRepresentation findUserById(String userId) {
        String token = "Bearer " + getClientToken();
        try {
            return keycloakFeignClient.findUserById(token, userId);
        } catch (FeignException e) {
            log.error("Cannot get user with id: {}, details: {}", userId, e.getMessage());
            throw new ExternalServiceException("Cannot get user from identity service", Map.of("details", e.getMessage()));
        }
    }


    /**
     * Create user
     * @param data user data
     * */
    public void createUser(UserRepresentation data) {
        String token = "Bearer " + getClientToken();
        try {
            keycloakFeignClient.createUser(token, data);
        } catch (FeignException e) {
            log.error("Cannot create user: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot invoke keycloak create user api!", Map.of("details", e.getMessage()));
        }
    }


    /**
     * Update user
     * @param data user data
     * */
    public void updateUser(String userId, UserRepresentation data) {
        String token = "Bearer " + getClientToken();
        try {
            keycloakFeignClient.updateUser(token, userId, data);
        } catch (FeignException e) {
            log.error("Cannot update user keycloak: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new BadRequestException("Cannot update user keycloak!", e);
        }
    }


    /**
     * Update user
     * @param data user data
     * */
    public void resetPassword(String userId, CredentialRepresentation data) {
        String token = "Bearer " + getClientToken();
        try {
            keycloakFeignClient.resetPassword(token, userId, data);
        } catch (FeignException e) {
            log.error("Cannot reset password: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot reset password", Map.of("details", e.getMessage()));
        }
    }


    public void assignRole(String userId, List<RoleRepresentation> data) {
        String token = "Bearer " + getClientToken();
        try {
            keycloakFeignClient.assignRole(token, userId, data);
        } catch (FeignException e) {
            log.error("Cannot assign role: {}, details: {}", JsonUtils.toJson(data), e.getMessage());
            throw new ExternalServiceException("Cannot assign role", Map.of("details", e.getMessage()));
        }
    }

    /**
     * Get users by role name
     * @param pageable pagination information
     * @param roleName name of the role
     * @return a page of users with the specified role
     */
    public Page<UserRepresentation> getUsersByRole(Pageable pageable, String roleName) {
        Integer page = pageable.getPageNumber();
        Integer size = pageable.getPageSize();
        Integer first = page * size;
        String token = "Bearer " + getClientToken();
        
        try {
            List<UserRepresentation> users = keycloakFeignClient.getUsersByRole(token, roleName, first, size);
            // Since Keycloak doesn't return a total count in this API, we need to make a separate call without pagination
            // to get the total count or estimate it based on the returned results
            List<UserRepresentation> allUsers = keycloakFeignClient.getUsersByRole(token, roleName, null, null);
            int total = allUsers.size();
            return new PageImpl<>(users, pageable, total);
        } catch (FeignException e) {
            log.error("Cannot get users by role {}: {}", roleName, e.getMessage());
            throw new ExternalServiceException("Cannot get users by role", e.getMessage());
        }
    }
    
    public MappingsRepresentation findRoles(String userId) {
        String token = "Bearer " + getClientToken();
        try {
            return keycloakFeignClient.getRoleMappings(token, userId);
        } catch (FeignException e) {
            log.error("Cannot get user role, id: {}, details: {}", userId, e.getMessage());
            throw new ExternalServiceException("Cannot get user role, id: " + userId, Map.of("details", e.getMessage()));
        }
    }

    /**
     * Logout user and revoke token
     * @throws ExternalServiceException if error occurs
     */
    public void logout() {
        try {
            String token = "Bearer " + getClientToken();
            String userId = TokenUtils.getUserId();
            keycloakFeignClient.logout(token, userId);
            log.info("User logged out successfully");
        } catch (FeignException e) {
            log.error("Cannot logout user: {}", e.getMessage());
            throw new ExternalServiceException("Cannot logout user", e.getMessage());
        }
    }

    public UserRepresentation getCurrentUser() {
        String userId = TokenUtils.getUserId();
        return findUserById(userId);
    }

    /**
     * Delete user from Keycloak
     * @param userId ID của user cần xóa
     * @throws ExternalServiceException if error occurs
     */
    public void deleteUser(String userId) {
        String token = "Bearer " + getClientToken();
        try {
            keycloakFeignClient.deleteUser(token, userId);
            log.info("User deleted successfully: {}", userId);
        } catch (FeignException e) {
            log.error("Cannot delete user: {}, details: {}", userId, e.getMessage());
            throw new ExternalServiceException("Cannot delete user", e.getMessage());
        }
    }

    public List<UserSessionRepresentation> getUserSessions(String userId) {
        try {
            String adminToken = "Bearer " + getMasterToken();
            return keycloakFeignClient.getUserSessions(adminToken, userId);
        } catch (FeignException e) {
            log.error("Error getting user sessions for user {}", userId, e);
            throw new ExternalServiceException("Failed to get user sessions", Map.of("details", e.getMessage()));
        }
    }

    public List<ResourceRepresentation> getResourcesOfPolicy(String token, String clientUuid, String policyId) {
        return keycloakFeignClient.getResourcesOfPolicy(token, clientUuid, policyId);
    }
}
