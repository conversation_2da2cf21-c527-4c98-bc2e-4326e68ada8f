package org.pronexus.user.domain.model.external;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class EmployeeModel {
    private Long id;
    private String userId;
    private String avatar;
    private String code;
    private String name;
    private String firstName;
    private String lastName;
    private Integer gender;
    private LocalDate dateOfBirth;
    private ContractType contractType;
    private List<Address> address;
    private Integer salary;
    private Long partnerId;
    private Integer departmentId;
    private String teamId;
    private EmployeeStatus status;
    private String identifierNo;
    private LocalDate issueDate;
    private String issuePlace;
    private String phoneNumber;
    private String email;
    private boolean canEditAccount;
    private List<BankInfo> bankAccounts;
    private Integer creditLimit;
    private Object department;
    private String position;
    private String note;

    // Passcode fields moved to UserPasscode entity
}
