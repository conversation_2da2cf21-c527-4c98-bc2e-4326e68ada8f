package org.pronexus.user.domain.service.impl;

import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.exception.base.DataValidationException;
import com.salaryadvance.commonlibrary.exception.base.ResourceNotFoundException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.idm.UserRepresentation;
import org.pronexus.user.app.dtos.otp.GenerateOtpRequestDto;
import org.pronexus.user.app.dtos.otp.GenerateOtpResponseDto;
import org.pronexus.user.app.dtos.otp.VerifyOtpRequestDto;
import org.pronexus.user.app.dtos.otp.VerifyOtpResponseDto;
import org.pronexus.user.domain.entity.Otp;
import org.pronexus.user.domain.entity.OtpDeliveryMethod;
import org.pronexus.user.domain.entity.OtpStatus;
import org.pronexus.user.domain.entity.OtpType;
import org.pronexus.user.domain.feign.adapter.KeycloakClient;
import org.pronexus.user.domain.mapper.OtpMapper;
import org.pronexus.user.domain.repository.OtpRepository;
import org.pronexus.user.domain.service.core.OtpService;
import org.pronexus.user.domain.service.core.ZnsService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Implementation của OtpService
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OtpServiceImpl implements OtpService {

    private final OtpRepository otpRepository;
    private final OtpMapper otpMapper;
    private final ZnsService znsService;
    private final KeycloakClient keycloakClient;

    private static final SecureRandom RANDOM = new SecureRandom();

    @Value("${otp.expiry.minutes:5}")
    private int otpExpiryMinutes;

    @Value("${otp.max.attempts:3}")
    private int maxAttempts;

    @Value("${otp.max.daily:10}")
    private int maxDailyOtps;

    @Value("${otp.length:6}")
    private int otpLength;

    @Value("${zns.otp.template.id}")
    private String templateId;

    /**
     * Tạo mã OTP mới
     */
    @Override
    @Transactional
    public CommandResponse<GenerateOtpResponseDto> generateOtp(GenerateOtpRequestDto requestDto) {
        try {
            // Luôn sử dụng contactInfo làm userId vì OTP xử lý cho người dùng chưa đăng
            // nhập
            String userId = requestDto.getContactInfo();
            log.info("Tạo OTP, sử dụng contactInfo làm userId: {}", userId);

            return generateOtpForUser(userId, requestDto);
        } catch (Exception e) {
            log.error("Lỗi khi tạo OTP: {}", e.getMessage(), e);
            return CommandResponse.failure(null, "Không thể tạo OTP: " + e.getMessage());
        }
    }

    /**
     * Tạo mã OTP mới với userId được chỉ định
     */
    @Override
    @Transactional
    public CommandResponse<GenerateOtpResponseDto> generateOtpForUser(String userId, GenerateOtpRequestDto requestDto) {
        try {
            // Vô hiệu hóa các OTP cũ cùng loại
            CommandResponse<Void> invalidateResponse = invalidateAllOtpsByUserAndType(userId, requestDto.getType());
            if (!invalidateResponse.isSuccess()) {
                return CommandResponse.failure(null, invalidateResponse.getMessage());
            }

            // Tạo OTP mới
            String otpCode = generateRandomOtp(otpLength);
            LocalDateTime expiryTime = LocalDateTime.now().plusMinutes(otpExpiryMinutes);

            // Lưu templateId vào additionalData nếu có
            String additionalData = requestDto.getAdditionalData();

            Otp otp = Otp.builder()
                    .code(otpCode)
                    .expiryTime(expiryTime)
                    .verificationAttempts(0)
                    .status(OtpStatus.PENDING)
                    .type(requestDto.getType())
                    .userId(userId)
                    .contactInfo(requestDto.getContactInfo())
                    .deliveryMethod(requestDto.getDeliveryMethod())
                    .additionalData(additionalData)
                    .build();

            otp = otpRepository.save(otp);

            // Gửi OTP
            boolean otpSent = sendOtp(otp);

            GenerateOtpResponseDto responseDto = otpMapper.toGenerateOtpResponseDto(otp);

            // Trả về thông báo phù hợp dựa trên kết quả gửi OTP
            if (otpSent) {
                return CommandResponse.success(responseDto, "Mã OTP đã được gửi thành công");
            } else {
                // OTP đã được tạo nhưng không gửi được qua ZNS
                log.warn("OTP đã được tạo nhưng không gửi được qua ZNS. OTP ID: {}, Phone: {}", otp.getId(),
                        otp.getContactInfo());
                return CommandResponse.success(responseDto,
                        "Mã OTP đã được tạo nhưng không thể gửi qua Zalo. Vui lòng kiểm tra cấu hình ZNS hoặc liên hệ quản trị viên.");
            }
        } catch (Exception e) {
            log.error("Lỗi khi tạo OTP cho user {}: {}", userId, e.getMessage(), e);
            return CommandResponse.failure(null, "Không thể tạo OTP: " + e.getMessage());
        }
    }

    /**
     * Xác thực mã OTP với tùy chọn cập nhật trạng thái
     */
    @Override
    @Transactional
    public CommandResponse<VerifyOtpResponseDto> verifyOtp(VerifyOtpRequestDto requestDto, boolean updateStatus) {
        try {
            // Luôn sử dụng contactInfo làm userId vì OTP xử lý cho người dùng chưa đăng
            // nhập
            String userId = requestDto.getContactInfo();
            log.info("Xác thực OTP, sử dụng contactInfo làm userId: {}, updateStatus: {}", userId, updateStatus);

            return verifyOtpForUser(userId, requestDto, updateStatus);
        } catch (Exception e) {
            log.error("Lỗi khi xác thực OTP: {}", e.getMessage(), e);
            return CommandResponse.failure(
                    VerifyOtpResponseDto.builder()
                            .success(false)
                            .type(requestDto.getType())
                            .message("Không thể xác thực OTP: " + e.getMessage())
                            .build(),
                    "Không thể xác thực OTP: " + e.getMessage());
        }
    }

    /**
     * Xác thực mã OTP với userId được chỉ định
     */
    @Override
    @Transactional
    public CommandResponse<VerifyOtpResponseDto> verifyOtpForUser(String userId, VerifyOtpRequestDto requestDto) {
        // Mặc định là cập nhật trạng thái
        return verifyOtpForUser(userId, requestDto, true);
    }

    /**
     * Xác thực mã OTP với userId được chỉ định và tùy chọn cập nhật trạng thái
     */
    @Override
    @Transactional
    public CommandResponse<VerifyOtpResponseDto> verifyOtpForUser(String userId, VerifyOtpRequestDto requestDto,
            boolean updateStatus) {
        // Tìm OTP hợp lệ (chưa hết hạn)
        Optional<Otp> validOtpOptional = otpRepository.findValidOtp(
                requestDto.getCode(), userId, OtpStatus.PENDING, LocalDateTime.now());

        if (validOtpOptional.isPresent()) {
            Otp otp = validOtpOptional.get();

            // Kiểm tra loại OTP
            if (otp.getType() != requestDto.getType()) {
                return CommandResponse.failure(
                        VerifyOtpResponseDto.builder()
                                .success(false)
                                .type(requestDto.getType())
                                .message("Loại OTP không khớp")
                                .build(),
                        "Loại OTP không khớp");
            }

            // OTP hợp lệ, tiếp tục xử lý
        } else {
            // Tìm OTP để kiểm tra lý do không hợp lệ
            Optional<Otp> otpOptional = otpRepository.findByCodeAndUserId(
                    requestDto.getCode(), userId);

            if (otpOptional.isPresent()) {
                Otp otp = otpOptional.get();

                // Kiểm tra OTP đã hết hạn chưa
                if (otp.getStatus() == OtpStatus.PENDING && otp.isExpired()) {
                    // Xóa OTP đã hết hạn thay vì cập nhật trạng thái
                    otpRepository.delete(otp);
                    log.info("Đã xóa OTP có ID: {} do đã hết hạn", otp.getId());

                    return CommandResponse.failure(
                            VerifyOtpResponseDto.builder()
                                    .success(false)
                                    .type(requestDto.getType())
                                    .message("Mã OTP đã hết hạn")
                                    .build(),
                            "Mã OTP đã hết hạn");
                } else {
                    return CommandResponse.failure(
                            VerifyOtpResponseDto.builder()
                                    .success(false)
                                    .type(requestDto.getType())
                                    .message("Mã OTP không hợp lệ hoặc đã hết hạn")
                                    .build(),
                            "Mã OTP không hợp lệ hoặc đã hết hạn");
                }
            } else {
                // Không tìm thấy OTP
                return CommandResponse.failure(
                        VerifyOtpResponseDto.builder()
                                .success(false)
                                .type(requestDto.getType())
                                .message("Mã OTP không hợp lệ")
                                .build(),
                        "Mã OTP không hợp lệ");
            }
        }

        // Lấy OTP hợp lệ để tiếp tục xử lý
        Otp otp = validOtpOptional.get();

        // Kiểm tra số lần thử
        otp.setVerificationAttempts(otp.getVerificationAttempts() + 1);

        if (otp.isMaxAttemptsReached(maxAttempts)) {
            // Xóa OTP khi vượt quá số lần thử thay vì cập nhật trạng thái
            otpRepository.delete(otp);
            log.info("Đã xóa OTP có ID: {} do vượt quá số lần thử", otp.getId());

            return CommandResponse.failure(
                    VerifyOtpResponseDto.builder()
                            .success(false)
                            .type(requestDto.getType())
                            .message("Đã vượt quá số lần thử. Vui lòng yêu cầu mã OTP mới")
                            .remainingAttempts(0)
                            .build(),
                    "Đã vượt quá số lần thử. Vui lòng yêu cầu mã OTP mới");
        }

        if (updateStatus) {
            // Cập nhật trạng thái OTP thành đã xác thực
            otp.setStatus(OtpStatus.VERIFIED);
            otpRepository.save(otp);
            log.info("OTP đã được xác thực thành công, ID: {}", otp.getId());
        }

        // todo: chưa xử lý đến, Nếu là OTP đăng ký, kích hoạt tài khoản
        // if (OtpType.REGISTRATION.equals(requestDto.getType())) {
        // try {
        // // Tìm user theo số điện thoại (username)
        // UserRepresentation user =
        // keycloakClient.findUserByUsername(otp.getContactInfo());
        //
        // // Kích hoạt tài khoản
        // user.setEnabled(true);
        //
        // // Cập nhật thuộc tính
        // Map<String, List<String>> attributes = user.getAttributes();
        // if (attributes == null) {
        // attributes = new HashMap<>();
        // }
        // attributes.put("phone_verified", List.of("true"));
        // attributes.put("registration_complete", List.of("true"));
        // user.setAttributes(attributes);
        //
        // // Lưu thay đổi
        // keycloakClient.updateUser(user.getId(), user);
        // log.info("User account activated after OTP verification: {}",
        // otp.getContactInfo());
        // } catch (Exception e) {
        // log.error("Failed to activate user account after OTP verification: {}",
        // e.getMessage());
        // // Không ném lỗi, vẫn trả về thành công vì OTP đã được xác thực
        // }
        // }

        // Tạo token xác thực nếu cần
        String verificationToken = null;
        if (OtpType.PASSWORD_RESET.equals(requestDto.getType())) {
            verificationToken = generateVerificationToken(userId, otp.getId());
        }

        return CommandResponse.success(
                VerifyOtpResponseDto.builder()
                        .success(true)
                        .type(requestDto.getType())
                        .message("Xác thực OTP thành công")
                        .verificationToken(verificationToken)
                        .build(),
                "Xác thực OTP thành công");
    }

    /**
     * Vô hiệu hóa tất cả OTP của một người dùng theo loại
     */
    @Override
    @Transactional
    public CommandResponse<Void> invalidateAllOtpsByUserAndType(String userId, OtpType type) {
        List<Otp> pendingOtps = otpRepository.findByUserIdAndTypeAndStatus(userId, type, OtpStatus.PENDING);

        if (!pendingOtps.isEmpty()) {
            // Cập nhật trạng thái thành INVALIDATED thay vì xóa để giữ lại lịch sử
            pendingOtps.forEach(otp -> {
                otp.setStatus(OtpStatus.INVALIDATED);
            });
            otpRepository.saveAll(pendingOtps);
            log.info("Đã vô hiệu hóa {} OTP cũ của người dùng {} với loại {}", pendingOtps.size(), userId, type);

            // Kiểm tra số lượng OTP đã tạo trong ngày
            long startOfDay = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).toEpochSecond(ZoneOffset.UTC) * 1000;
            int dailyOtpCount = otpRepository.countByUserIdAndTypeAndCreatedAtAfter(userId, type, startOfDay);

            if (dailyOtpCount >= maxDailyOtps) {
                log.warn("Người dùng {} đã tạo {} OTP loại {} trong ngày, vượt quá giới hạn {}",
                        userId, dailyOtpCount, type, maxDailyOtps);
                return CommandResponse.failure(null,
                        "Bạn đã yêu cầu quá nhiều mã OTP trong ngày. Vui lòng thử lại vào ngày mai.");
            }
        }

        return CommandResponse.success(null, "Đã vô hiệu hóa tất cả OTP cũ");
    }

    /**
     * Vô hiệu hóa OTP theo ID
     */
    @Override
    @Transactional
    public CommandResponse<Void> invalidateOtp(Long otpId) {
        Otp otp = otpRepository.findById(otpId)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy OTP", Map.of("id", otpId)));

        // Xóa hoàn toàn OTP thay vì cập nhật trạng thái
        otpRepository.delete(otp);
        log.info("Đã xóa OTP có ID: {}", otpId);

        return CommandResponse.success(null, "Đã xóa OTP");
    }

    /**
     * Tạo mã OTP ngẫu nhiên
     */
    private String generateRandomOtp(int length) {
        StringBuilder otp = new StringBuilder();
        for (int i = 0; i < length; i++) {
            otp.append(RANDOM.nextInt(10));
        }
        return otp.toString();
    }

    /**
     * Gửi OTP qua Zalo ZNS
     * 
     * @return true nếu gửi thành công, false nếu có lỗi
     */
    private boolean sendOtp(Otp otp) {
        try {
            // Sử dụng templateId từ biến môi trường
            org.pronexus.user.domain.feign.dto.ZnsResponse response = znsService
                    .sendOtpNotification(otp.getContactInfo(), otp.getCode(), templateId, otpExpiryMinutes);

            // Kiểm tra response từ ZNS
            if (response != null && response.isSuccess()) {
                log.info("Gửi OTP thành công qua ZNS cho số điện thoại: {}, OTP ID: {}", otp.getContactInfo(),
                        otp.getId());
                return true;
            } else {
                log.warn("Gửi OTP thất bại qua ZNS cho số điện thoại: {}, OTP ID: {}, Error: {}, Message: {}",
                        otp.getContactInfo(), otp.getId(),
                        response != null ? response.getError() : "null",
                        response != null ? response.getMessage() : "null response");
                return false;
            }
        } catch (Exception e) {
            log.error("Lỗi khi gửi OTP qua ZNS cho số điện thoại: {}, OTP ID: {}, Error: {}",
                    otp.getContactInfo(), otp.getId(), e.getMessage(), e);
            // Không ném ra ngoại lệ, chỉ ghi log lỗi và trả về false
            return false;
        }
    }

    /**
     * Lấy mục đích sử dụng OTP dựa trên loại
     */
    private String getOtpPurpose(OtpType type) {
        return switch (type) {
            case PASSWORD_RESET -> "đặt lại mật khẩu";
            case TRANSACTION_VERIFICATION -> "xác thực giao dịch";
            case REGISTRATION -> "đăng ký tài khoản";
            case LOGIN_VERIFICATION -> "xác thực đăng nhập";
            case RESET_PASSCODE -> "đặt lại passcode";
        };
    }

    /**
     * Tạo token xác thực
     */
    private String generateVerificationToken(String userId, Long otpId) {
        // Trong thực tế, bạn nên sử dụng JWT hoặc một phương pháp an toàn khác
        // Đây chỉ là ví dụ đơn giản
        return userId + "_" + otpId + "_" + System.currentTimeMillis();
    }

    /**
     * Lấy mã OTP mới nhất theo số điện thoại và loại OTP (chỉ dùng cho mục đích
     * test)
     */
    @Override
    public CommandResponse<String> getLatestOtpForTesting(String contactInfo, OtpType type) {
        // Luôn sử dụng contactInfo làm userId vì OTP xử lý cho người dùng chưa đăng
        // nhập
        String userId = contactInfo;
        log.info("Lấy mã OTP mới nhất cho mục đích test, userId: {}, type: {}", userId, type);

        // Tìm OTP mới nhất theo userId và type
        List<Otp> pendingOtps = otpRepository.findByUserIdAndTypeAndStatus(userId, type, OtpStatus.PENDING);

        if (pendingOtps.isEmpty()) {
            return CommandResponse.failure(null, "Không tìm thấy OTP nào cho số điện thoại này");
        }

        // Sắp xếp theo thời gian tạo giảm dần để lấy OTP mới nhất
        pendingOtps.sort((o1, o2) -> Long.compare(o2.getCreatedAt(), o1.getCreatedAt()));
        Otp latestOtp = pendingOtps.get(0);

        return CommandResponse.success(latestOtp.getCode(), "Lấy mã OTP thành công");
    }
}
