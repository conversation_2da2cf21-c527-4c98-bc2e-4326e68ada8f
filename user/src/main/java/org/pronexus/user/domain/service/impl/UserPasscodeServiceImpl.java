package org.pronexus.user.domain.service.impl;

import com.salaryadvance.commonlibrary.utils.TokenUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.app.dtos.UserPasscodeInfoDto;
import org.pronexus.user.domain.feign.adapter.PortalClient;
import org.pronexus.user.domain.service.core.UserPasscodeService;
import org.springframework.stereotype.Service;

/**
 * Implementation của UserPasscodeService
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserPasscodeServiceImpl implements UserPasscodeService {

    private final PortalClient portalClient;

    @Override
    public UserPasscodeInfoDto getCurrentUserPasscodeInfo() {
        String userId = TokenUtils.getUserId();
        return getUserPasscodeInfo(userId);
    }

    @Override
    public UserPasscodeInfoDto getUserPasscodeInfo(String userId) {
        try {
            log.debug("Getting passcode info for user: {}", userId);
            UserPasscodeInfoDto passcodeInfo = portalClient.getUserPasscodeInfo(userId);
            
            if (passcodeInfo == null) {
                log.debug("No passcode info found for user: {}", userId);
                // Trả về default info nếu không tìm thấy
                return UserPasscodeInfoDto.builder()
                        .userId(userId)
                        .passcodeEnabled(false)
                        .passcodeFailedAttempts(0)
                        .build();
            }
            
            return passcodeInfo;
        } catch (Exception e) {
            log.error("Error getting passcode info for user {}: {}", userId, e.getMessage());
            // Trả về default info nếu có lỗi
            return UserPasscodeInfoDto.builder()
                    .userId(userId)
                    .passcodeEnabled(false)
                    .passcodeFailedAttempts(0)
                    .build();
        }
    }
}
