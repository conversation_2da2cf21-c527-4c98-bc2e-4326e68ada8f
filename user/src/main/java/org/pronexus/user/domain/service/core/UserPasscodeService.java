package org.pronexus.user.domain.service.core;

import org.pronexus.user.app.dtos.UserPasscodeInfoDto;

/**
 * Service interface để quản lý thông tin passcode của user
 */
public interface UserPasscodeService {
    
    /**
     * L<PERSON>y thông tin passcode của user hiện tại
     * 
     * @return Thông tin passcode của user, null nếu user chưa có passcode
     */
    UserPasscodeInfoDto getCurrentUserPasscodeInfo();
    
    /**
     * Lấy thông tin passcode của user theo userId
     * 
     * @param userId ID của user trong Keycloak
     * @return Thông tin passcode của user, null nếu user chưa có passcode
     */
    UserPasscodeInfoDto getUserPasscodeInfo(String userId);
}
