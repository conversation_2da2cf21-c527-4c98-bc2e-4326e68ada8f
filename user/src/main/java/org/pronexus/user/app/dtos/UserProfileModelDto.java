package org.pronexus.user.app.dtos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.keycloak.representations.idm.MappingsRepresentation;
import org.keycloak.representations.idm.UserSessionRepresentation;
import org.pronexus.user.domain.model.external.*;

import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserProfileModelDto {
    private String id;
    private String username;
    private Boolean enabled;
    private Long employeeId;
    private String avatar;
    private String code;
    private String name;
    private Integer gender;
    private LocalDate dateOfBirth;
    private ContractType contractType;
    private List<Address> address;
    private Long partnerId;
    private Long departmentId;
    private Long teamId;
    private EmployeeStatus status;
    private String identifierNo;
    private LocalDate issueDate;
    private String issuePlace;
    private String phoneNumber;
    private String email;
    private boolean canEditAccount;
    private List<BankInfo> bankAccounts;
    private Integer creditLimit;
    private PartnerModel partner;
    private MappingsRepresentation roleMapping;

    // User sessions
    private List<UserSessionRepresentation> userSessions;

    // Passcode information
    private UserPasscodeInfoDto passcode;
}
