package org.pronexus.user.app.dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO chứa thông tin passcode của user
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPasscodeInfoDto {
    
    /**
     * ID của user trong Keycloak
     */
    private String userId;
    
    /**
     * Thời gian hết hạn của passcode
     */
    private LocalDateTime passcodeExpiryDate;
    
    /**
     * Số lần nhập sai passcode
     */
    private Integer passcodeFailedAttempts;
    
    /**
     * Thời gian reset passcode lần cuối
     */
    private LocalDateTime passcodeLastResetDate;
    
    /**
     * Trạng thái bật/tắt passcode
     */
    private Boolean passcodeEnabled;
    
    /**
     * Kiểm tra xem passcode có bị khóa không
     */
    public boolean isLocked() {
        return passcodeFailedAttempts != null && passcodeFailedAttempts >= 3;
    }
    
    /**
     * Kiểm tra xem passcode có hết hạn không
     */
    public boolean isExpired() {
        return passcodeExpiryDate != null && passcodeExpiryDate.isBefore(LocalDateTime.now());
    }
}
