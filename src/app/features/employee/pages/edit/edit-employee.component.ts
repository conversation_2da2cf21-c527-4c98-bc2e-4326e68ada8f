import { Component, Injector, isDevMode, ViewChild } from '@angular/core'
import { BUTTON_TYPE_BACK, BUTTON_UPDATE, ComponentAbstract, HttpOptions, MessageSeverity, Status } from '@shared'
import { CheckboxControlComponent, SelectControlComponent, TextControlComponent } from '@shared/components/data-input'
import { TreeSelectControlComponent } from '@shared/components/data-input/tree-select-control/tree-select-control.component'
import { MatNativeDateModule } from '@angular/material/core'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { FormBankUsedComponent } from '@features/partner/shared/form-bank-used/form-bank-used.component'
import { TableFeeServiceComponent } from '@features/partner/shared/table-fee-service/table-fee-service.component'
import { ContactInfoComponent } from '@features/partner/shared/contact-info/contact-info.component'
import { FormFooterComponent } from '@shared/components/section/form-footer/form-footer.component'
import { debounceTime, finalize, Observable, takeUntil } from 'rxjs'
import { BtnFooter } from '@shared/models'
import { AppSelectAddressControlComponent } from '@shared/components/data-input/app-select-address-control/app-select-address-control.component'

import { EmployeeService } from '@features/employee/services/employee.service'
import { DateControlComponent } from '@shared/components/data-input/date-control/date-control.component'
import { blobToJson, cleanObject, getPropertyKeys, parseTimeStamp } from '@shared/utils'
import { ROUTES_NAME_SYSTEM_MANAGEMENT_EMPLOYEE } from '@features/employee/constants'
import { ImageFileDragDropComponent } from '@shared/components/data-input/image-file-drag-drop/image-file-drag-drop.component'
import { environment } from '@env/environment'
import {
  fieldAccountNumber,
  fieldAddress,
  fieldAllowChange,
  fieldBank,
  fieldBirthDate,
  fieldContractType,
  fieldDepartment,
  fieldEmail,
  fieldEmployeeCode,
  fieldGender,
  fieldGTTT,
  fieldIssueDate,
  fieldIssuePlace,
  fieldLimit,
  fieldName,
  fieldNote,
  fieldPhone,
  fieldPosition,
  fieldSalary,
  fieldStatus,
  fieldTeam
} from '@features/employee/form/create.form'

@Component({
  selector: 'app-edit-employee',
  imports: [
    TextControlComponent,
    SelectControlComponent,
    MatNativeDateModule,
    TextareaControlComponent,
    FormFooterComponent,
    CheckboxControlComponent,
    AppSelectAddressControlComponent,
    TreeSelectControlComponent,
    DateControlComponent,
    ImageFileDragDropComponent
  ],
  templateUrl: './edit-employee.component.html',
  standalone: true,
  styleUrl: './edit-employee.component.scss'
})
export class EditEmployeeComponent extends ComponentAbstract {
  $fieldEmployeeCode = fieldEmployeeCode()
  $fieldName = fieldName()
  $fieldGender = fieldGender()
  $fieldBirthDate = fieldBirthDate()
  $fieldPhone = fieldPhone()
  $fieldEmail = fieldEmail()
  $fieldContractType = fieldContractType()
  $fieldStatus = fieldStatus()
  $fieldGTTT = fieldGTTT()
  $fieldIssueDate = fieldIssueDate()
  $fieldIssuePlace = fieldIssuePlace()
  $fieldLimit = fieldLimit()
  $fieldSalary = fieldSalary()
  $fieldPosition = fieldPosition()
  $fieldTeam = fieldTeam()
  $fieldDepartment = fieldDepartment()
  $fieldBank = fieldBank()
  $fieldAccountNumber = fieldAccountNumber()
  $fieldAllowChange = fieldAllowChange()
  $fieldNote = fieldNote()
  $fieldAddress = fieldAddress()

  listButton: Observable<BtnFooter[]>
  employeeId: string = ''

  @ViewChild('selectAddressControlComponent') selectAddressControlComponent: AppSelectAddressControlComponent
  @ViewChild('bank') bank: FormBankUsedComponent
  @ViewChild('fee') fee: TableFeeServiceComponent
  @ViewChild('userInfo') userInfo: ContactInfoComponent
  private fileAvatar: File
  imageUrlEdit: string

  constructor(
    protected override injector: Injector,
    private service: EmployeeService
  ) {
    super(injector)
    this.employeeId = this.route.snapshot.paramMap.get('id')
    this.form.valueChanges.pipe(debounceTime(300)).subscribe((value) => {
      console.log(value)
    })
  }

  protected componentInit(): void {
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_BACK, BUTTON_UPDATE)
    this.service.getDetail(this.employeeId).subscribe({
      next: (value: any) => {
        const res = cleanObject(value.data)
        if (res?.avatar) {
          this.imageUrlEdit = `${environment.services.file}/v1/file/${res?.avatar}/download`
        }
        if (res?.departmentId) {
          res['departmentId'] = `${res?.departmentId}`
        }
        if (res?.creditLimit) {
          res['creditLimit'] = `${res?.creditLimit}`
        }
        if (String(res?.gender) === '0' || String(res?.gender) === '1') {
          res['gender'] = `${res?.gender}`
        }

        this.form.patchValue(res)
        this.form.get(this.$fieldBirthDate.key).patchValue(new Date(res.dateOfBirth))
        this.form.get(this.$fieldIssueDate.key).patchValue(new Date(res.issueDate))

        if (res.address && res.address[0]) {
          const addressData = res.address[0]
          this.form.get(this.$fieldAddress.key).patchValue({
            province: addressData.province, // Ánh xạ city → province
            district: addressData.district, // Giữ nguyên district
            ward: addressData.ward, // Giữ nguyên ward
            address: addressData.address // Ánh xạ detail → address
          })
        }
        if (res.bankAccounts && res.bankAccounts.length > 0) {
          this.form.get(this.$fieldBank.key).patchValue(res.bankAccounts[0].bankCode)
          this.form.get(this.$fieldAccountNumber.key).patchValue(res.bankAccounts[0].bankAccountNumber)
        }
        this.form.get(this.$fieldAllowChange.key).patchValue(res.allowChange ? '1' : undefined)
        // this.form.get(this.$fieldDepartment.key).patchValue(`${res.departmentId}`)

        console.log('LEGA', this.form)
      },
      error: (err) => {},
      complete: () => {}
    })
  }

  onClickBtn($event: any) {
    switch ($event) {
      case BUTTON_UPDATE.typeBtn:
        this.checkAndUpdateForm().then((r) => {})
        break
      case BUTTON_TYPE_BACK.typeBtn:
        this.goTo(ROUTES_NAME_SYSTEM_MANAGEMENT_EMPLOYEE.LIST)
        break
    }
  }

  /**
   * call api update ns
   * @param data
   */
  async checkAndUpdateForm() {
    this.validateAllFields(this.form)
    const formData = this.form.getRawValue()
    const data = {
      ...formData,
      creditlimit: Number(formData.creditlimit),
      bankAccounts: [
        {
          bankCode: formData?.bankCode,
          bankAccountNumber: formData[this.$fieldAccountNumber.key]
        }
      ],
      address: [formData.address],
      issueDate: parseTimeStamp(formData.issueDate),
      dateOfBirth: parseTimeStamp(formData.dateOfBirth),
      allowChange: !!this.form.get(this.$fieldAllowChange.key).value
    }

    if (this.form.valid) {
      if (this.fileAvatar instanceof File) {
        const rsUploadFile: any = await this.uploadImage(this.fileAvatar)
        if (rsUploadFile?.data.details?.path) {
          const fileId = rsUploadFile?.data?.details?.id
          // const filePath = rsUploadFile?.data.details?.path
          this.updateEmployee({ ...data, avatar: fileId })
        }
      } else {
        this.updateEmployee(data)
      }
    }
  }

  fileChanged($event: File[]) {
    console.log($event?.length)
    if ($event) {
      this.fileAvatar = $event[0]
    }
  }

  /**
   * call api update ns
   * @param data
   */
  updateEmployee(data: any) {
    this.indicator.showActivityIndicator(true)
    this.service
      .updateEmployee(this.employeeId, data)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe({
        next: (res: any) => {
          if (res?.httpStatusCode === Status.SUCCESS) {
            this.form.reset()
            this.goTo(ROUTES_NAME_SYSTEM_MANAGEMENT_EMPLOYEE.LIST)
            this.showDialogSuccessI18n('', 'Sửa nhân sự <br/>thành công')
          }
        },
        error: (err) => {
          this.showDialogErrorI18n(err?.error?.data?.message, 'Sửa nhân sự <br/>thất bại')
        },
        complete: () => {}
      })
  }

  uploadImage($event: any) {
    return new Promise((resolve, reject) => {
      const formData: FormData = new FormData()
      formData.append('file', $event)

      const options: HttpOptions = {
        url: environment.hostApi,
        // path: `${this.urlImport}?preview=${preview}`,
        path: `${environment.services.file}/v1/file/upload`,
        params: {}
      }

      this.httpClient
        .uploadFormData(options, formData)
        .pipe(
          takeUntil(this.ngUnsubscribe),
          finalize(() => this.indicator.hideActivityIndicator(true))
        )
        .subscribe({
          next: (res) => {
            console.log('wth omg', res)
            if (res?.body?.type === 'application/json') {
              blobToJson(res.body)
                .then((jsonObject) => {
                  console.log(jsonObject)
                  resolve(jsonObject)
                })
                .catch((error) => {
                  console.error('Lỗi khi chuyển đổi Blob sang JSON:', error)
                })
            }
          },
          error: (res) => {
            if (res?.error?.type === 'application/json') {
              blobToJson(res?.error)
                .then((jsonObject) => {
                  this.toastr.showToastri18n(jsonObject?.message, '', MessageSeverity.error)
                })
                .catch((error) => {
                  console.error('Lỗi khi chuyển đổi Blob sang JSON:', error)
                })
            }
          }
        })
    })
  }
}
