# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    exec-prefab 599ms
    [gap of 33ms]
  generate-prefab-packages completed in 637ms
  execute-generate-process
    exec-configure 774ms
    [gap of 136ms]
  execute-generate-process completed in 911ms
  [gap of 34ms]
generate_cxx_metadata completed in 1726ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 122ms
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 153ms

