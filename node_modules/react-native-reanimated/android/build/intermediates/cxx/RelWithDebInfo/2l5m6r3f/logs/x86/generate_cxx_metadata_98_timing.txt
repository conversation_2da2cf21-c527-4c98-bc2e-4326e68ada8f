# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    exec-prefab 509ms
  generate-prefab-packages completed in 512ms
  execute-generate-process
    exec-configure 571ms
    [gap of 26ms]
  execute-generate-process completed in 598ms
generate_cxx_metadata completed in 1115ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 19ms
generate_cxx_metadata completed in 28ms

