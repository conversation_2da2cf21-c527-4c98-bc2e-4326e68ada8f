# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    exec-prefab 870ms
  generate-prefab-packages completed in 874ms
  execute-generate-process
    exec-configure 581ms
    [gap of 35ms]
  execute-generate-process completed in 617ms
generate_cxx_metadata completed in 1494ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 61ms
generate_cxx_metadata completed in 68ms

