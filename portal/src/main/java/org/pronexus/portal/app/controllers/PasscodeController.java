package org.pronexus.portal.app.controllers;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.rest.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.portal.app.dtos.UserPasscodeInfoDto;
import org.pronexus.portal.app.dtos.employee.ResetPasscodeCommandDto;
import org.pronexus.portal.app.dtos.employee.ResetPasscodeWithOtpCommandDto;
import org.pronexus.portal.app.dtos.employee.SetupPasscodeCommandDto;
import org.pronexus.portal.app.dtos.employee.VerifyPasscodeCommandDto;
import org.pronexus.portal.domain.services.core.PasscodeInfoService;
import org.pronexus.portal.domain.services.core.PasscodeService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for managing employee passcodes for transfer confirmation
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/passcode")
@RequiredArgsConstructor
public class PasscodeController {

    private final PasscodeService passcodeService;
    private final PasscodeInfoService passcodeInfoService;

    /**
     * Setup a new passcode
     * 
     * @param command DTO containing passcode and confirmation
     * @return Response with success or error message
     */
    @PostMapping("/setup")
    public ResponseEntity<Response<String>> setupPasscode(@RequestBody SetupPasscodeCommandDto command) {
        CommandResponse<Void> response = passcodeService.setupPasscode(command);
        if (response.isSuccess()) {
            return Response.success(response.getMessage());
        } else {
            return Response.of(HttpStatus.BAD_REQUEST, null);
        }
    }

    /**
     * Verify a passcode for transaction confirmation
     * 
     * @param command DTO containing passcode and transaction ID
     * @return Response with success or error message
     */
    @PostMapping("/verify")
    public ResponseEntity<Response<String>> verifyPasscode(@RequestBody VerifyPasscodeCommandDto command) {
        CommandResponse<Void> response = passcodeService.verifyPasscode(command);
        if (response.isSuccess()) {
            return Response.success(response.getMessage());
        } else {
            return Response.of(HttpStatus.BAD_REQUEST, null);
        }
    }

    /**
     * Reset an existing passcode
     * 
     * @param command DTO containing old passcode, new passcode and confirmation
     * @return Response with success or error message
     */
    @PostMapping("/reset")
    public ResponseEntity<Response<Void>> resetPasscode(@RequestBody ResetPasscodeCommandDto command) {
        CommandResponse<Void> response = passcodeService.resetPasscode(command);
        if (response.isSuccess()) {
            return Response.success(null);
        } else {
            return Response.of(HttpStatus.BAD_REQUEST, null);
        }
    }

    /**
     * Disable passcode for the current employee
     *
     * @return Response with success or error message
     */
    @PostMapping("/disable")
    public ResponseEntity<Response<Void>> disablePasscode() {
        CommandResponse<Void> response = passcodeService.disablePasscode();
        if (response.isSuccess()) {
            return Response.success(null);
        } else {
            return Response.of(HttpStatus.BAD_REQUEST, null);
        }
    }

    /**
     * Reset passcode using OTP verification
     * Note: phoneNumber is now required in the request body
     *
     * @param command DTO containing phoneNumber, OTP and new passcode information
     * @return Response with success or error message
     */
    @PostMapping("/reset-with-otp")
    public ResponseEntity<Response<CommandResponse<Void>>> resetPasscodeWithOtp(
            @RequestBody ResetPasscodeWithOtpCommandDto command) {
        CommandResponse<Void> response = passcodeService.resetPasscodeWithOtp(command);
        return Response.success(response);
    }

    /**
     * Check if passcode is enabled for the current employee
     *
     * @return Response with boolean status
     */
    @GetMapping("/status")
    public ResponseEntity<Response<Boolean>> isPasscodeEnabled() {
        CommandResponse<Boolean> response = passcodeService.isPasscodeEnabled();
        if (response.isSuccess()) {
            return Response.success(response.getDetails());
        } else {
            return Response.of(HttpStatus.BAD_REQUEST, null);
        }
    }

    /**
     * Lấy thông tin passcode của user theo userId
     * API này được sử dụng bởi User Service để lấy thông tin passcode
     *
     * @param userId ID của user trong Keycloak
     * @return Thông tin passcode của user
     */
    @GetMapping("/info/{userId}")
    public ResponseEntity<Response<UserPasscodeInfoDto>> getUserPasscodeInfo(@PathVariable String userId) {
        try {
            UserPasscodeInfoDto passcodeInfo = passcodeInfoService.getUserPasscodeInfo(userId);
            return Response.success(passcodeInfo);
        } catch (Exception e) {
            log.error("Error getting passcode info for user {}: {}", userId, e.getMessage());
            return Response.of(HttpStatus.BAD_REQUEST, null);
        }
    }
}
