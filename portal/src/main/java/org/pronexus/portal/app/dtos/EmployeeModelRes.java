package org.pronexus.portal.app.dtos;

import com.salaryadvance.commonlibrary.model.BaseDto;
import lombok.Getter;
import lombok.Setter;
import org.pronexus.portal.app.response.DepartmentRes;
import org.pronexus.portal.domain.entities.Address;
import org.pronexus.portal.domain.entities.BankInfo;
import org.pronexus.portal.domain.entities.type.ContractType;
import org.pronexus.portal.domain.entities.type.EmployeeStatus;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
public class EmployeeModelRes extends BaseDto {
    private Long id;
    private String userId;
    private String avatar;
    private String code;
    private String name;
    private Integer gender;
    private LocalDate dateOfBirth;
    private ContractType contractType;
    private List<Address> address;
    private Integer salary;
    private Long partnerId;
    private Integer departmentId;
    private String teamId;
    private EmployeeStatus status;
    private String identifierNo;
    private LocalDate issueDate;
    private String issuePlace;
    private String phoneNumber;
    private String email;
    private boolean canEditAccount;
    private List<BankInfo> bankAccounts;
    private Integer creditLimit;
    private DepartmentRes department;
    private String position;
    private String note;

    // Passcode fields moved to UserPasscode entity
}
