package org.pronexus.portal.app.controllers;

import com.salaryadvance.commonlibrary.rest.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.portal.app.dtos.UserPasscodeInfoDto;
import org.pronexus.portal.domain.services.core.PasscodeInfoService;
import org.springframework.web.bind.annotation.*;

/**
 * Controller để cung cấp thông tin passcode cho các service khác
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/passcode")
@RequiredArgsConstructor
public class PasscodeInfoController {

    private final PasscodeInfoService passcodeInfoService;

    /**
     * Lấy thông tin passcode của user theo userId
     * 
     * @param userId ID của user trong Keycloak
     * @return Thông tin passcode của user
     */
    @GetMapping("/info/{userId}")
    public Response<UserPasscodeInfoDto> getUserPasscodeInfo(@PathVariable String userId) {
        try {
            UserPasscodeInfoDto passcodeInfo = passcodeInfoService.getUserPasscodeInfo(userId);
            return Response.success(passcodeInfo, "Lấy thông tin passcode thành công");
        } catch (Exception e) {
            log.error("Error getting passcode info for user {}: {}", userId, e.getMessage());
            return Response.failure(null, "Không thể lấy thông tin passcode: " + e.getMessage());
        }
    }
}
