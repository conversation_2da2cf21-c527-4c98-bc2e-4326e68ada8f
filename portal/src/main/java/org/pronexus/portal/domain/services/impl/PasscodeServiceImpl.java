package org.pronexus.portal.domain.services.impl;

import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.exception.ForbiddenException;
import com.salaryadvance.commonlibrary.exception.base.DataValidationException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.portal.app.dtos.employee.ResetPasscodeCommandDto;
import org.pronexus.portal.app.dtos.employee.ResetPasscodeWithOtpCommandDto;
import org.pronexus.portal.app.dtos.employee.SetupPasscodeCommandDto;
import org.pronexus.portal.app.dtos.employee.VerifyPasscodeCommandDto;
import org.pronexus.portal.app.dtos.otp.VerifyOtpRequestDto;
import org.pronexus.portal.domain.entities.Employee;
import org.pronexus.portal.domain.entities.UserPasscode;
import org.pronexus.portal.domain.feign.clients.UserFeignClient;
import org.pronexus.portal.domain.repositories.EmployeeRepository;
import org.pronexus.portal.domain.repositories.UserPasscodeRepository;
import org.pronexus.portal.domain.services.core.PasscodeService;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Implementation of the PasscodeService for managing employee passcodes
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PasscodeServiceImpl implements PasscodeService {

    private final EmployeeRepository employeeRepository;
    private final UserPasscodeRepository userPasscodeRepository;
    private final PasswordEncoder passwordEncoder;
    private final ConfigurationServiceImpl configurationService;
    private final UserFeignClient userFeignClient;

    // Số lần nhập sai tối đa trước khi khóa
    private static final int MAX_FAILED_ATTEMPTS = 3;
    // Thời gian hết hạn passcode (30 ngày)
    private static final int PASSCODE_EXPIRY_DAYS = 30;

    @Override
    @Transactional
    public CommandResponse<Void> setupPasscode(SetupPasscodeCommandDto command) {
        String userId = TokenUtils.getUserId();

        validatePasscode(command.getPasscode(), command.getConfirmPasscode());

        // Find or create user passcode
        UserPasscode userPasscode = userPasscodeRepository.findByUserId(userId)
                .orElse(new UserPasscode());

        // If user already has passcode enabled, require old passcode verification
        if (Boolean.TRUE.equals(userPasscode.getPasscodeEnabled())) {
            if (command.getOldPasscode() == null) {
                throw new BadRequestException("Mã passcode cũ là bắt buộc khi thay đổi passcode mới");
            }

            if (!passwordEncoder.matches(command.getOldPasscode(), userPasscode.getPasscode())) {
                throw new BadRequestException("Mã passcode cũ không chính xác");
            }
        }

        // Set user ID if this is a new record
        if (userPasscode.getUserId() == null) {
            userPasscode.setUserId(userId);
        }

        userPasscode.setPasscode(passwordEncoder.encode(command.getPasscode()));
        userPasscode.setPasscodeExpiryDate(LocalDateTime.now().plusDays(PASSCODE_EXPIRY_DAYS));
        userPasscode.setPasscodeFailedAttempts(0);
        userPasscode.setPasscodeLastResetDate(LocalDateTime.now());
        userPasscode.setPasscodeEnabled(true);

        userPasscodeRepository.save(userPasscode);

        log.info("Passcode setup successfully for user: {}", userId);
        return CommandResponse.success(null, "Thiết lập mã passcode thành công");
    }

    @Override
    @Transactional
    public CommandResponse<Void> verifyPasscode(VerifyPasscodeCommandDto command) {
        String userId = TokenUtils.getUserId();

        // Get user passcode
        UserPasscode userPasscode = userPasscodeRepository.findByUserId(userId)
                .orElseThrow(() -> new BadRequestException("Bạn cần thiết lập passcode trước khi xác thực"));

        // Todo: mac dinh luon bat passcode
        if (!Boolean.TRUE.equals(userPasscode.getPasscodeEnabled())) {
            throw new BadRequestException("Bạn cần bật passcode trước khi xác thực");
        }
        //
        // if (userPasscode.getPasscodeExpiryDate().isBefore(LocalDateTime.now())) {
        // throw new BadRequestException("Passcode has expired. Please reset your
        // passcode");
        // }
        //
        // if (userPasscode.getPasscodeFailedAttempts() >= MAX_FAILED_ATTEMPTS) {
        // throw new ForbiddenException("Passcode is locked due to too many failed
        // attempts");
        // }

        if (!passwordEncoder.matches(command.getPasscode(), userPasscode.getPasscode())) {
            userPasscode.setPasscodeFailedAttempts(userPasscode.getPasscodeFailedAttempts() + 1);
            userPasscodeRepository.save(userPasscode);

            if (userPasscode.getPasscodeFailedAttempts() >= MAX_FAILED_ATTEMPTS) {
                log.warn("Passcode locked for user: {}", userId);
                throw new ForbiddenException("Passcode khóa do quá nhiều lần nhập sai");
            }

            throw new BadRequestException("Sai passcode. Vui lòng thử lại");
        }

        // Xác thực thành công, reset số lần thất bại
        userPasscode.setPasscodeFailedAttempts(0);
        userPasscodeRepository.save(userPasscode);

        // Xác nhận giao dịch nếu có transactionId
        if (command.getTransactionId() != null) {
            // TODO: Integrate with transaction confirmation service
            // transManagementService.confirmTransaction(command.getTransactionId());
            log.info("Transaction confirmed with ID: {}", command.getTransactionId());
        }

        log.info("Passcode verified successfully for user: {}", userId);
        return CommandResponse.success(null, "Xác thực passcode thành công");
    }

    @Override
    @Transactional
    public CommandResponse<Void> resetPasscode(ResetPasscodeCommandDto command) {
        String userId = TokenUtils.getUserId();

        // Get user passcode
        UserPasscode userPasscode = userPasscodeRepository.findByUserId(userId)
                .orElseThrow(() -> new BadRequestException("Passcode chưa được thiết lập"));

        if (!Boolean.TRUE.equals(userPasscode.getPasscodeEnabled())) {
            throw new BadRequestException("Passcode chưa được bật");
        }

        // Xác thực passcode cũ
        if (!passwordEncoder.matches(command.getOldPasscode(), userPasscode.getPasscode())) {
            throw new DataValidationException("Mã passcode cũ không chính xác", null);
        }

        validatePasscode(command.getNewPasscode(), command.getConfirmPasscode());

        userPasscode.setPasscode(passwordEncoder.encode(command.getNewPasscode()));
        userPasscode.setPasscodeExpiryDate(LocalDateTime.now().plusDays(PASSCODE_EXPIRY_DAYS));
        userPasscode.setPasscodeFailedAttempts(0);
        userPasscode.setPasscodeLastResetDate(LocalDateTime.now());

        userPasscodeRepository.save(userPasscode);

        log.info("Passcode reset successfully for user: {}", userId);
        return CommandResponse.success(null, "Đổi mã passcode thành công.");
    }

    @Override
    @Transactional
    public CommandResponse<Void> disablePasscode() {
        String userId = TokenUtils.getUserId();

        // Get user passcode
        UserPasscode userPasscode = userPasscodeRepository.findByUserId(userId)
                .orElseThrow(() -> new BadRequestException("Passcode chưa được thiết lập"));

        userPasscode.setPasscodeEnabled(false);

        userPasscodeRepository.save(userPasscode);

        log.info("Passcode disabled for user: {}", userId);
        return CommandResponse.success(null, "Tắt mã passcode thành công");
    }

    @Override
    public CommandResponse<Boolean> isPasscodeEnabled() {
        String userId = TokenUtils.getUserId();

        // Get user passcode
        UserPasscode userPasscode = userPasscodeRepository.findByUserId(userId)
                .orElse(null);

        boolean isEnabled = userPasscode != null && Boolean.TRUE.equals(userPasscode.getPasscodeEnabled());
        return CommandResponse.success(isEnabled, "Trạng thái passcode đã được lấy");
    }

    @Override
    @Transactional
    public CommandResponse<Void> resetPasscodeWithOtp(ResetPasscodeWithOtpCommandDto command) {
        String userId = TokenUtils.getUserId();

        // Lấy thông tin employee để có phoneNumber cho OTP verification
        Employee employee = findEmployeeByUserId(userId);

        // Validate new passcode
        validatePasscode(command.getNewPasscode(), command.getConfirmPasscode());

        // Verify OTP
        try {
            VerifyOtpRequestDto verifyRequest = VerifyOtpRequestDto.builder()
                    .type("RESET_PASSCODE")
                    .contactInfo(employee.getPhoneNumber())
                    .code(command.getOtp())
                    .build();

            String token = getAuthorizationToken();
            var verifyResponse = userFeignClient.verifyOtp(token, true, verifyRequest);

            if (verifyResponse.getBody() == null ||
                    verifyResponse.getBody().getData() == null ||
                    !verifyResponse.getBody().getData().isSuccess()) {
                throw new BadRequestException("OTP không hợp lệ hoặc đã hết hạn");
            }

            // Find or create user passcode
            UserPasscode userPasscode = userPasscodeRepository.findByUserId(userId)
                    .orElse(new UserPasscode());

            // Set user ID if this is a new record
            if (userPasscode.getUserId() == null) {
                userPasscode.setUserId(userId);
            }

            // Update passcode
            userPasscode.setPasscode(passwordEncoder.encode(command.getNewPasscode()));
            userPasscode.setPasscodeExpiryDate(LocalDateTime.now().plusDays(PASSCODE_EXPIRY_DAYS));
            userPasscode.setPasscodeFailedAttempts(0);
            userPasscode.setPasscodeLastResetDate(LocalDateTime.now());
            userPasscode.setPasscodeEnabled(true);

            userPasscodeRepository.save(userPasscode);

            log.info("Passcode reset with OTP successfully for user: {}", userId);
            return CommandResponse.success(null, "Thay đổi passcode thành công");

        } catch (Exception e) {
            log.error("Error verifying OTP for passcode reset: {}", e.getMessage());
            throw new BadRequestException("Không thể xác thực OTP: " + e.getMessage());
        }
    }

    /**
     * Validate passcode format and matching confirmation
     * 
     * @param passcode        The passcode to validate
     * @param confirmPasscode The confirmation passcode
     * @throws BadRequestException if validation fails
     */
    private void validatePasscode(String passcode, String confirmPasscode) {
        if (passcode == null || passcode.length() < 6) {
            throw new BadRequestException("Mã passcode phải đủ 6 ký tự");
        }

        if (!passcode.equals(confirmPasscode)) {
            throw new BadRequestException("Mã passcode và xác nhận không khớp");
        }
    }

    /**
     * Lấy giá trị cấu hình theo key
     *
     * @param key          Key của cấu hình
     * @param defaultValue Giá trị mặc định nếu không tìm thấy cấu hình
     * @return Giá trị cấu hình dạng String
     */
    private String getConfigurationString(String key, String defaultValue) {
        try {
            return configurationService.getConfigurationString(key, defaultValue);
        } catch (Exception e) {
            log.error("Lỗi khi lấy cấu hình {}: {}", key, e.getMessage());
        }
        return defaultValue;
    }

    /**
     * Tìm nhân viên theo userId với thông báo lỗi tùy chỉnh
     *
     * @param userId ID của người dùng
     * @return Employee đối tượng nhân viên
     * @throws BadRequestException nếu không tìm thấy nhân viên
     */
    private Employee findEmployeeByUserId(String userId) {
        String hotline = getConfigurationString("CUSTOMER_SUPPORT_HOTLINE", "1900xxxx");
        String customMessage = "Thông tin bạn khai báo không chính xác, vui lòng liên hệ số hotline " + hotline
                + " để được hướng dẫn";
        return employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new BadRequestException(customMessage));
    }

    /**
     * Lấy authorization token từ security context
     */
    private String getAuthorizationToken() {
        Jwt jwt = (Jwt) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return "Bearer " + jwt.getTokenValue();
    }
}
