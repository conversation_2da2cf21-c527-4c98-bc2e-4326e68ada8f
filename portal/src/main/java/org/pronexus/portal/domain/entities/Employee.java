package org.pronexus.portal.domain.entities;

import com.salaryadvance.commonlibrary.persistence.AuditableEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.pronexus.portal.domain.entities.type.ContractType;
import org.pronexus.portal.domain.entities.type.EmployeeStatus;

import java.time.LocalDate;
import java.util.List;

@Data
@Entity
@Table(name = "employee", schema = "portal")
@EqualsAndHashCode(callSuper = false)
public class Employee extends AuditableEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String userId;
    private String avatar;
    private String code;
    private String name;
    private Integer gender;
    private LocalDate dateOfBirth;
    @Enumerated(EnumType.STRING)
    private ContractType contractType;
    @JdbcTypeCode(SqlTypes.JSON)
    private List<Address> address;
    private Integer salary;
    private Long partnerId;
    private Integer departmentId;
    private String teamId;
    private String position;
    @Enumerated(EnumType.STRING)
    private EmployeeStatus status;
    private String identifierNo;
    private LocalDate issueDate;
    private String issuePlace;
    private String phoneNumber;
    private String email;
    private boolean canEditAccount;
    @JdbcTypeCode(SqlTypes.JSON)
    private List<BankInfo> bankAccounts;
    private Integer creditLimit;
    private String note;

    // Quan hệ 1-nhiều với EmployeeSalaryHistory, tự động xóa lịch sử lương khi xóa
    // nhân sự
    @OneToMany(mappedBy = "employee", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<EmployeeSalaryHistory> salaryHistories;

    // Passcode fields moved to UserPasscode entity
}
