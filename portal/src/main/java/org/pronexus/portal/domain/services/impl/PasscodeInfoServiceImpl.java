package org.pronexus.portal.domain.services.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.portal.app.dtos.UserPasscodeInfoDto;
import org.pronexus.portal.domain.entities.UserPasscode;
import org.pronexus.portal.domain.repositories.UserPasscodeRepository;
import org.pronexus.portal.domain.services.core.PasscodeInfoService;
import org.springframework.stereotype.Service;

/**
 * Implementation của PasscodeInfoService
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PasscodeInfoServiceImpl implements PasscodeInfoService {

    private final UserPasscodeRepository userPasscodeRepository;

    @Override
    public UserPasscodeInfoDto getUserPasscodeInfo(String userId) {
        log.debug("Getting passcode info for user: {}", userId);
        
        UserPasscode userPasscode = userPasscodeRepository.findByUserId(userId)
                .orElse(null);
        
        if (userPasscode == null) {
            log.debug("No passcode found for user: {}", userId);
            return UserPasscodeInfoDto.builder()
                    .userId(userId)
                    .passcodeEnabled(false)
                    .passcodeFailedAttempts(0)
                    .build();
        }
        
        return UserPasscodeInfoDto.builder()
                .userId(userPasscode.getUserId())
                .passcodeExpiryDate(userPasscode.getPasscodeExpiryDate())
                .passcodeFailedAttempts(userPasscode.getPasscodeFailedAttempts())
                .passcodeLastResetDate(userPasscode.getPasscodeLastResetDate())
                .passcodeEnabled(userPasscode.getPasscodeEnabled())
                .build();
    }
}
