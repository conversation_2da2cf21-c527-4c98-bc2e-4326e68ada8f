package org.pronexus.portal.domain.repositories;

import org.pronexus.portal.domain.entities.UserPasscode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository để thao tác với entity UserPasscode
 */
@Repository
public interface UserPasscodeRepository extends JpaRepository<UserPasscode, Long> {
    
    /**
     * Tìm passcode theo userId
     * 
     * @param userId ID của user trong Keycloak
     * @return Optional chứa UserPasscode nếu tìm thấy
     */
    Optional<UserPasscode> findByUserId(String userId);
    
    /**
     * Kiểm tra xem user đã có passcode chưa
     * 
     * @param userId ID của user trong Keycloak
     * @return true nếu user đã có passcode, false nếu chưa
     */
    boolean existsByUserId(String userId);
    
    /**
     * Xóa passcode theo userId
     * 
     * @param userId ID của user trong Keycloak
     */
    void deleteByUserId(String userId);
}
