package org.pronexus.portal.domain.services.core;

import org.pronexus.portal.app.dtos.UserPasscodeInfoDto;

/**
 * Service interface để cung cấp thông tin passcode cho các service khác
 */
public interface PasscodeInfoService {
    
    /**
     * L<PERSON>y thông tin passcode của user theo userId
     * 
     * @param userId ID của user trong Keycloak
     * @return Thông tin passcode của user, null nếu user chưa có passcode
     */
    UserPasscodeInfoDto getUserPasscodeInfo(String userId);
}
