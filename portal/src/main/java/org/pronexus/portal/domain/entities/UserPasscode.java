package org.pronexus.portal.domain.entities;

import com.salaryadvance.commonlibrary.persistence.AuditableEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Entity lưu trữ thông tin passcode của user
 * Map theo userId (ID của user Keycloak)
 */
@Data
@Entity
@Table(name = "user_passcode", schema = "portal")
@EqualsAndHashCode(callSuper = false)
public class UserPasscode extends AuditableEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * ID của user trong Keycloak
     */
    @Column(nullable = false, unique = true)
    private String userId;
    
    /**
     * Mã passcode đã được mã hóa
     */
    @Column(nullable = true)
    private String passcode;
    
    /**
     * Thời gian hết hạn của passcode
     */
    @Column(nullable = true)
    private LocalDateTime passcodeExpiryDate;
    
    /**
     * <PERSON><PERSON> lần nhập sai passcode
     */
    @Column(nullable = true)
    private Integer passcodeFailedAttempts = 0;
    
    /**
     * Th<PERSON><PERSON> gian reset passcode lần cuối
     */
    @Column(nullable = true)
    private LocalDateTime passcodeLastResetDate;
    
    /**
     * Trạng thái bật/tắt passcode
     */
    @Column(nullable = false)
    private Boolean passcodeEnabled = false;
}
