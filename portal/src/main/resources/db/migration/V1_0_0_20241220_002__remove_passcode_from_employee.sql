-- Migration script to remove passcode fields from employee table
-- This should be run AFTER the user_passcode table is created and data is migrated
-- Author: System Migration
-- Date: 2024-12-20

-- WARNING: This script will permanently remove passcode columns from employee table
-- Make sure the migration to user_passcode table is successful before running this

-- Step 1: Drop passcode-related columns from employee table
ALTER TABLE portal.employee DROP COLUMN IF EXISTS passcode;
ALTER TABLE portal.employee DROP COLUMN IF EXISTS passcode_expiry_date;
ALTER TABLE portal.employee DROP COLUMN IF EXISTS passcode_failed_attempts;
ALTER TABLE portal.employee DROP COLUMN IF EXISTS passcode_last_reset_date;
ALTER TABLE portal.employee DROP COLUMN IF EXISTS passcode_enabled;

-- Step 2: Add comment to document the change
COMMENT ON TABLE portal.employee IS 'Employee table - passcode fields moved to user_passcode table';
