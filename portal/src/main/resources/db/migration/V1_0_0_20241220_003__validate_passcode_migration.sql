-- Validation script to check passcode migration from employee to user_passcode table
-- Author: System Migration
-- Date: 2024-12-20

-- This script provides validation queries to ensure the migration was successful
-- Run these queries manually to verify the migration

-- Query 1: Check total count of migrated records
-- Expected: Should match the count of employees with passcode_enabled = true
SELECT 
    'Migration Count Check' as check_type,
    (SELECT COUNT(*) FROM portal.user_passcode) as user_passcode_count,
    (SELECT COUNT(*) FROM portal.employee WHERE passcode_enabled = true) as employee_with_passcode_count;

-- Query 2: Check for any missing user_ids in migration
-- Expected: Should return 0 rows if migration is complete
SELECT 
    'Missing User IDs' as check_type,
    e.user_id,
    e.passcode_enabled
FROM portal.employee e
WHERE e.passcode_enabled = true
  AND e.user_id IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM portal.user_passcode up 
      WHERE up.user_id = e.user_id
  );

-- Query 3: Check for data consistency between tables
-- Expected: All fields should match for migrated records
SELECT 
    'Data Consistency Check' as check_type,
    e.user_id,
    CASE 
        WHEN e.passcode = up.passcode THEN 'MATCH'
        ELSE 'MISMATCH'
    END as passcode_match,
    CASE 
        WHEN e.passcode_enabled = up.passcode_enabled THEN 'MATCH'
        ELSE 'MISMATCH'
    END as enabled_match,
    CASE 
        WHEN e.passcode_failed_attempts = up.passcode_failed_attempts THEN 'MATCH'
        ELSE 'MISMATCH'
    END as failed_attempts_match
FROM portal.employee e
JOIN portal.user_passcode up ON e.user_id = up.user_id
WHERE e.passcode_enabled = true;

-- Query 4: Check for duplicate user_ids in user_passcode table
-- Expected: Should return 0 rows (user_id should be unique)
SELECT 
    'Duplicate User IDs' as check_type,
    user_id,
    COUNT(*) as count
FROM portal.user_passcode
GROUP BY user_id
HAVING COUNT(*) > 1;

-- Query 5: Summary statistics
SELECT 
    'Summary Statistics' as check_type,
    (SELECT COUNT(*) FROM portal.user_passcode) as total_user_passcodes,
    (SELECT COUNT(*) FROM portal.user_passcode WHERE passcode_enabled = true) as enabled_passcodes,
    (SELECT COUNT(*) FROM portal.user_passcode WHERE passcode IS NOT NULL) as passcodes_with_value,
    (SELECT COUNT(*) FROM portal.user_passcode WHERE passcode_failed_attempts > 0) as passcodes_with_failed_attempts;
