# Passcode Migration Guide

## Tổng quan
Migration này di chuyển việc lưu trữ passcode từ bảng `employee` sang bảng mới `user_passcode`, map theo `user_id` (ID của user Keycloak).

## Lý do thay đổi
- Tách biệt logic passcode khỏi thông tin employee
- Map trực tiếp theo user_id thay vì thông qua employee
- <PERSON><PERSON> dàng quản lý và mở rộng tính năng passcode

## Các file migration

### 1. V1_0_0_20241220_001__create_user_passcode_table.sql
- Tạo bảng `user_passcode` mới
- Migrate dữ liệu từ bảng `employee` sang `user_passcode`
- Chỉ migrate các employee có `passcode_enabled = true`

### 2. V1_0_0_20241220_002__remove_passcode_from_employee.sql
- Xóa các trường passcode khỏi bảng `employee`
- **CẢNH BÁO**: Chỉ chạy sau khi đã validate migration thành công

### 3. V1_0_0_20241220_003__validate_passcode_migration.sql
- Script validation để kiểm tra migration
- Chạy thủ công để verify dữ liệu

## Cấu trúc bảng user_passcode

```sql
CREATE TABLE portal.user_passcode (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL UNIQUE,
    passcode VARCHAR(255),
    passcode_expiry_date TIMESTAMP,
    passcode_failed_attempts INTEGER DEFAULT 0,
    passcode_last_reset_date TIMESTAMP,
    passcode_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255)
);
```

## Thay đổi code

### 1. Entity mới
- `UserPasscode.java`: Entity mới để lưu trữ passcode
- `UserPasscodeRepository.java`: Repository cho UserPasscode

### 2. Service cập nhật
- `PasscodeServiceImpl.java`: Cập nhật để sử dụng UserPasscode thay vì Employee

### 3. Entity cập nhật
- `Employee.java`: Xóa các trường passcode
- `EmployeeModel.java`: Xóa các trường passcode

## Quy trình migration

### Bước 1: Backup dữ liệu
```sql
-- Backup bảng employee trước khi migration
CREATE TABLE portal.employee_backup AS SELECT * FROM portal.employee;
```

### Bước 2: Chạy migration đầu tiên
```bash
# Migration sẽ tự động chạy khi start application
# Hoặc chạy thủ công:
# V1_0_0_20241220_001__create_user_passcode_table.sql
```

### Bước 3: Validate migration
```sql
-- Chạy các query trong file validation
\i V1_0_0_20241220_003__validate_passcode_migration.sql
```

### Bước 4: Xóa trường passcode khỏi employee (sau khi validate)
```bash
# Chỉ chạy sau khi đã validate thành công
# V1_0_0_20241220_002__remove_passcode_from_employee.sql
```

## Kiểm tra sau migration

1. **Kiểm tra số lượng record**:
   ```sql
   SELECT COUNT(*) FROM portal.user_passcode;
   ```

2. **Kiểm tra tính năng passcode**:
   - Test setup passcode
   - Test verify passcode
   - Test reset passcode
   - Test disable passcode

3. **Kiểm tra log**:
   - Xem log application để đảm bảo không có lỗi
   - Kiểm tra các API passcode hoạt động bình thường

## Rollback (nếu cần)

Nếu cần rollback:

1. **Restore từ backup**:
   ```sql
   DROP TABLE portal.employee;
   CREATE TABLE portal.employee AS SELECT * FROM portal.employee_backup;
   ```

2. **Revert code changes**:
   - Checkout về commit trước khi thay đổi
   - Rebuild và deploy lại

## Lưu ý quan trọng

- ⚠️ **KHÔNG** xóa trường passcode khỏi employee cho đến khi đã test đầy đủ
- ⚠️ **PHẢI** backup dữ liệu trước khi migration
- ⚠️ **PHẢI** validate migration trước khi xóa trường cũ
- ⚠️ Test đầy đủ tất cả tính năng passcode sau migration
