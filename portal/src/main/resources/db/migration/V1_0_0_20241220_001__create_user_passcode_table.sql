-- Migration script to create user_passcode table and migrate data from employee table
-- Author: System Migration
-- Date: 2024-12-20

-- Step 1: Create user_passcode table
CREATE TABLE IF NOT EXISTS portal.user_passcode (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL UNIQUE,
    passcode <PERSON><PERSON><PERSON><PERSON>(255),
    passcode_expiry_date TIMESTAMP,
    passcode_failed_attempts INTEGER DEFAULT 0,
    passcode_last_reset_date TIMESTAMP,
    passcode_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by <PERSON><PERSON><PERSON><PERSON>(255)
);

-- Step 2: Create index for better performance
CREATE INDEX IF NOT EXISTS idx_user_passcode_user_id ON portal.user_passcode(user_id);

-- Step 3: Migrate existing passcode data from employee table to user_passcode table
INSERT INTO portal.user_passcode (
    user_id,
    passcode,
    passcode_expiry_date,
    passcode_failed_attempts,
    passcode_last_reset_date,
    passcode_enabled,
    created_at,
    updated_at,
    created_by,
    updated_by
)
SELECT 
    e.user_id,
    e.passcode,
    e.passcode_expiry_date,
    COALESCE(e.passcode_failed_attempts, 0),
    e.passcode_last_reset_date,
    COALESCE(e.passcode_enabled, FALSE),
    e.created_at,
    e.updated_at,
    e.created_by,
    e.updated_by
FROM portal.employee e
WHERE e.user_id IS NOT NULL
  AND e.passcode_enabled = TRUE
  AND NOT EXISTS (
      SELECT 1 FROM portal.user_passcode up 
      WHERE up.user_id = e.user_id
  );

-- Step 4: Add comment to table
COMMENT ON TABLE portal.user_passcode IS 'Table to store user passcode information mapped by user_id (Keycloak user ID)';
COMMENT ON COLUMN portal.user_passcode.user_id IS 'Keycloak user ID';
COMMENT ON COLUMN portal.user_passcode.passcode IS 'Encrypted passcode';
COMMENT ON COLUMN portal.user_passcode.passcode_expiry_date IS 'Passcode expiry date';
COMMENT ON COLUMN portal.user_passcode.passcode_failed_attempts IS 'Number of failed passcode attempts';
COMMENT ON COLUMN portal.user_passcode.passcode_last_reset_date IS 'Last passcode reset date';
COMMENT ON COLUMN portal.user_passcode.passcode_enabled IS 'Whether passcode is enabled for the user';
