package org.pronexus.portal.app.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.pronexus.portal.app.dtos.UserPasscodeInfoDto;
import org.pronexus.portal.domain.services.core.PasscodeInfoService;
import org.pronexus.portal.domain.services.core.PasscodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Test class for PasscodeController
 */
@ExtendWith(MockitoExtension.class)
@WebMvcTest(PasscodeController.class)
class PasscodeControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PasscodeService passcodeService;

    @MockBean
    private PasscodeInfoService passcodeInfoService;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String TEST_USER_ID = "test-user-id";
    private UserPasscodeInfoDto testPasscodeInfo;

    @BeforeEach
    void setUp() {
        testPasscodeInfo = UserPasscodeInfoDto.builder()
                .userId(TEST_USER_ID)
                .passcodeEnabled(true)
                .passcodeFailedAttempts(0)
                .passcodeExpiryDate(LocalDateTime.now().plusDays(30))
                .passcodeLastResetDate(LocalDateTime.now())
                .build();
    }

    @Test
    void getUserPasscodeInfo_WithValidUserId_ShouldReturnPasscodeInfo() throws Exception {
        // Given
        when(passcodeInfoService.getUserPasscodeInfo(TEST_USER_ID)).thenReturn(testPasscodeInfo);

        // When & Then
        mockMvc.perform(get("/api/v1/passcode/info/{userId}", TEST_USER_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.userId").value(TEST_USER_ID))
                .andExpect(jsonPath("$.data.passcodeEnabled").value(true))
                .andExpect(jsonPath("$.data.passcodeFailedAttempts").value(0));
    }

    @Test
    void getUserPasscodeInfo_WithNonExistentUser_ShouldReturnNull() throws Exception {
        // Given
        when(passcodeInfoService.getUserPasscodeInfo(anyString())).thenReturn(null);

        // When & Then
        mockMvc.perform(get("/api/v1/passcode/info/{userId}", "non-existent-user")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isEmpty());
    }

    @Test
    void getUserPasscodeInfo_WhenServiceThrowsException_ShouldReturnInternalServerError() throws Exception {
        // Given
        when(passcodeInfoService.getUserPasscodeInfo(anyString()))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        mockMvc.perform(get("/api/v1/passcode/info/{userId}", TEST_USER_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void getUserPasscodeInfo_WithUserHavingLockedPasscode_ShouldReturnLockedInfo() throws Exception {
        // Given
        UserPasscodeInfoDto lockedPasscode = UserPasscodeInfoDto.builder()
                .userId(TEST_USER_ID)
                .passcodeEnabled(true)
                .passcodeFailedAttempts(3) // Locked
                .passcodeExpiryDate(LocalDateTime.now().plusDays(30))
                .passcodeLastResetDate(LocalDateTime.now())
                .build();

        when(passcodeInfoService.getUserPasscodeInfo(TEST_USER_ID)).thenReturn(lockedPasscode);

        // When & Then
        mockMvc.perform(get("/api/v1/passcode/info/{userId}", TEST_USER_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.userId").value(TEST_USER_ID))
                .andExpect(jsonPath("$.data.passcodeEnabled").value(true))
                .andExpect(jsonPath("$.data.passcodeFailedAttempts").value(3));
    }

    @Test
    void getUserPasscodeInfo_WithUserHavingExpiredPasscode_ShouldReturnExpiredInfo() throws Exception {
        // Given
        UserPasscodeInfoDto expiredPasscode = UserPasscodeInfoDto.builder()
                .userId(TEST_USER_ID)
                .passcodeEnabled(true)
                .passcodeFailedAttempts(0)
                .passcodeExpiryDate(LocalDateTime.now().minusDays(1)) // Expired
                .passcodeLastResetDate(LocalDateTime.now().minusDays(31))
                .build();

        when(passcodeInfoService.getUserPasscodeInfo(TEST_USER_ID)).thenReturn(expiredPasscode);

        // When & Then
        mockMvc.perform(get("/api/v1/passcode/info/{userId}", TEST_USER_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.userId").value(TEST_USER_ID))
                .andExpect(jsonPath("$.data.passcodeEnabled").value(true))
                .andExpect(jsonPath("$.data.passcodeFailedAttempts").value(0));
    }
}
