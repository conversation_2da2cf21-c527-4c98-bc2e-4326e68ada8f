package org.pronexus.portal.domain.services.impl;

import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.exception.ForbiddenException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.pronexus.portal.app.dtos.employee.SetupPasscodeCommandDto;
import org.pronexus.portal.app.dtos.employee.VerifyPasscodeCommandDto;
import org.pronexus.portal.domain.entities.Employee;
import org.pronexus.portal.domain.entities.UserPasscode;
import org.pronexus.portal.domain.feign.clients.UserFeignClient;
import org.pronexus.portal.domain.repositories.EmployeeRepository;
import org.pronexus.portal.domain.repositories.UserPasscodeRepository;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Test class for PasscodeServiceImpl after migration to UserPasscode
 */
@ExtendWith(MockitoExtension.class)
class PasscodeServiceImplTest {

    @Mock
    private EmployeeRepository employeeRepository;

    @Mock
    private UserPasscodeRepository userPasscodeRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private ConfigurationServiceImpl configurationService;

    @Mock
    private UserFeignClient userFeignClient;

    @InjectMocks
    private PasscodeServiceImpl passcodeService;

    private static final String TEST_USER_ID = "test-user-id";
    private static final String TEST_PASSCODE = "123456";
    private static final String ENCODED_PASSCODE = "encoded-passcode";

    private Employee testEmployee;
    private UserPasscode testUserPasscode;

    @BeforeEach
    void setUp() {
        testEmployee = new Employee();
        testEmployee.setId(1L);
        testEmployee.setUserId(TEST_USER_ID);
        testEmployee.setPhoneNumber("0123456789");

        testUserPasscode = new UserPasscode();
        testUserPasscode.setId(1L);
        testUserPasscode.setUserId(TEST_USER_ID);
        testUserPasscode.setPasscode(ENCODED_PASSCODE);
        testUserPasscode.setPasscodeEnabled(true);
        testUserPasscode.setPasscodeFailedAttempts(0);
        testUserPasscode.setPasscodeExpiryDate(LocalDateTime.now().plusDays(30));
        testUserPasscode.setPasscodeLastResetDate(LocalDateTime.now());
    }

    @Test
    void setupPasscode_NewUser_ShouldCreateNewPasscode() {
        // Given
        SetupPasscodeCommandDto command = new SetupPasscodeCommandDto();
        command.setPasscode(TEST_PASSCODE);
        command.setConfirmPasscode(TEST_PASSCODE);

        try (MockedStatic<TokenUtils> tokenUtils = mockStatic(TokenUtils.class)) {
            tokenUtils.when(TokenUtils::getUserId).thenReturn(TEST_USER_ID);

            when(userPasscodeRepository.findByUserId(TEST_USER_ID)).thenReturn(Optional.empty());
            when(passwordEncoder.encode(TEST_PASSCODE)).thenReturn(ENCODED_PASSCODE);
            when(userPasscodeRepository.save(any(UserPasscode.class))).thenReturn(testUserPasscode);

            // When
            CommandResponse<Void> result = passcodeService.setupPasscode(command);

            // Then
            assertTrue(result.isSuccess());
            assertEquals("Thiết lập mã passcode thành công", result.getMessage());
            verify(userPasscodeRepository).save(any(UserPasscode.class));
        }
    }

    @Test
    void verifyPasscode_ValidPasscode_ShouldSucceed() {
        // Given
        VerifyPasscodeCommandDto command = new VerifyPasscodeCommandDto();
        command.setPasscode(TEST_PASSCODE);

        try (MockedStatic<TokenUtils> tokenUtils = mockStatic(TokenUtils.class)) {
            tokenUtils.when(TokenUtils::getUserId).thenReturn(TEST_USER_ID);

            when(userPasscodeRepository.findByUserId(TEST_USER_ID)).thenReturn(Optional.of(testUserPasscode));
            when(passwordEncoder.matches(TEST_PASSCODE, ENCODED_PASSCODE)).thenReturn(true);

            // When
            CommandResponse<Void> result = passcodeService.verifyPasscode(command);

            // Then
            assertTrue(result.isSuccess());
            assertEquals("Xác thực passcode thành công", result.getMessage());
            verify(userPasscodeRepository).save(testUserPasscode);
            assertEquals(0, testUserPasscode.getPasscodeFailedAttempts());
        }
    }

    @Test
    void verifyPasscode_InvalidPasscode_ShouldIncrementFailedAttempts() {
        // Given
        VerifyPasscodeCommandDto command = new VerifyPasscodeCommandDto();
        command.setPasscode("wrong-passcode");

        try (MockedStatic<TokenUtils> tokenUtils = mockStatic(TokenUtils.class)) {
            tokenUtils.when(TokenUtils::getUserId).thenReturn(TEST_USER_ID);

            when(userPasscodeRepository.findByUserId(TEST_USER_ID)).thenReturn(Optional.of(testUserPasscode));
            when(passwordEncoder.matches("wrong-passcode", ENCODED_PASSCODE)).thenReturn(false);

            // When & Then
            assertThrows(BadRequestException.class, () -> passcodeService.verifyPasscode(command));
            verify(userPasscodeRepository).save(testUserPasscode);
            assertEquals(1, testUserPasscode.getPasscodeFailedAttempts());
        }
    }

    @Test
    void verifyPasscode_MaxFailedAttempts_ShouldThrowForbiddenException() {
        // Given
        VerifyPasscodeCommandDto command = new VerifyPasscodeCommandDto();
        command.setPasscode("wrong-passcode");
        testUserPasscode.setPasscodeFailedAttempts(2); // Already 2 failed attempts

        try (MockedStatic<TokenUtils> tokenUtils = mockStatic(TokenUtils.class)) {
            tokenUtils.when(TokenUtils::getUserId).thenReturn(TEST_USER_ID);

            when(userPasscodeRepository.findByUserId(TEST_USER_ID)).thenReturn(Optional.of(testUserPasscode));
            when(passwordEncoder.matches("wrong-passcode", ENCODED_PASSCODE)).thenReturn(false);

            // When & Then
            assertThrows(ForbiddenException.class, () -> passcodeService.verifyPasscode(command));
            assertEquals(3, testUserPasscode.getPasscodeFailedAttempts());
        }
    }

    @Test
    void isPasscodeEnabled_UserWithPasscode_ShouldReturnTrue() {
        // Given
        try (MockedStatic<TokenUtils> tokenUtils = mockStatic(TokenUtils.class)) {
            tokenUtils.when(TokenUtils::getUserId).thenReturn(TEST_USER_ID);

            when(userPasscodeRepository.findByUserId(TEST_USER_ID)).thenReturn(Optional.of(testUserPasscode));

            // When
            CommandResponse<Boolean> result = passcodeService.isPasscodeEnabled();

            // Then
            assertTrue(result.isSuccess());
            assertTrue(result.getDetails());
        }
    }

    @Test
    void isPasscodeEnabled_UserWithoutPasscode_ShouldReturnFalse() {
        // Given
        try (MockedStatic<TokenUtils> tokenUtils = mockStatic(TokenUtils.class)) {
            tokenUtils.when(TokenUtils::getUserId).thenReturn(TEST_USER_ID);

            when(userPasscodeRepository.findByUserId(TEST_USER_ID)).thenReturn(Optional.empty());

            // When
            CommandResponse<Boolean> result = passcodeService.isPasscodeEnabled();

            // Then
            assertTrue(result.isSuccess());
            assertFalse(result.getDetails());
        }
    }

    @Test
    void disablePasscode_ExistingPasscode_ShouldDisable() {
        // Given
        try (MockedStatic<TokenUtils> tokenUtils = mockStatic(TokenUtils.class)) {
            tokenUtils.when(TokenUtils::getUserId).thenReturn(TEST_USER_ID);

            when(employeeRepository.findByUserId(TEST_USER_ID)).thenReturn(Optional.of(testEmployee));
            when(userPasscodeRepository.findByUserId(TEST_USER_ID)).thenReturn(Optional.of(testUserPasscode));

            // When
            CommandResponse<Void> result = passcodeService.disablePasscode();

            // Then
            assertTrue(result.isSuccess());
            assertEquals("Tắt mã passcode thành công", result.getMessage());
            assertFalse(testUserPasscode.getPasscodeEnabled());
            verify(userPasscodeRepository).save(testUserPasscode);
        }
    }
}
