/**
 * The root navigator is used to switch between major navigation flows of your app.
 * Generally speaking, it will contain an auth flow (registration, login, forgot password)
 * and a "main" flow (which is contained in your PrimaryNavigator) which the user
 * will use once logged in.
 */
import React, { useContext, useEffect, useState, useRef } from 'react'
import { NavigationContainer, NavigationContainerRef, useNavigation } from '@react-navigation/native'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import { TabNavigator } from './primary-navigator'
import { AuthStackNavigator } from './auth-navigator'
import { SCREENS } from './screens'
import { Platform } from 'react-native'
import { AppLoadingScreen, Onboarding } from '../screens';
import { useStores } from '@app/models'
import messaging from '@react-native-firebase/messaging'
import { registerAppWithFCM, requestUserPermission } from '@app/services'
import { LogEvent } from '../services/loggingServices'
import PushNotification from 'react-native-push-notification'
import { ModalContext } from '@app/components/modal-success'
import useAppState from 'react-native-appstate-hook'
import Geolocation from '@react-native-community/geolocation'
import { save } from '@app/utils/storage'
import { AuthContext } from '@app/context/auth-context'; // Import AuthContext
import { EventRegister } from 'react-native-event-listeners'
import { AppBadge } from '@app/utils/appBadge'

const Stack = createNativeStackNavigator()

function RootStack(props) {
  const { state } = props
  // const navigation = useNavigation<any>()
  __DEV__ && console.log('RootStack State App', props.state)
  return (
    <Stack.Navigator
      initialRouteName={state?.isOnBoarding ? SCREENS.onBoarding : SCREENS.primaryStack}
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}
    >
      <>
        <Stack.Screen name={SCREENS.onBoarding} component={Onboarding} />
        <Stack.Screen name={SCREENS.primaryStack} component={TabNavigator} />
        <Stack.Screen name={SCREENS.appLoading} component={AppLoadingScreen} />
        <Stack.Screen name={SCREENS.authStack} component={AuthStackNavigator} />
      </>
    </Stack.Navigator>
  )
}

interface ComponentProps {
  state: any
}

export type NavigationContainerProps = React.ComponentProps<typeof NavigationContainer> & ComponentProps

export const RootNavigator = React.forwardRef<
  NavigationContainerRef<any>,
  Partial<NavigationContainerProps>
>((props: any, ref: any) => {
  const { profileStore, notificationStore } = useStores()
  const { showSuccess, showError, showWarning } = useContext(ModalContext)
  const [watchId, setWatchId] = useState<number | null>(null)
  const authContext = useContext(AuthContext) // Use AuthContext
  const isShowingSessionExpiredDialog = useRef(false);

  useAppState({
    onChange: (newAppState) => {
      __DEV__ && console.log('RootNavigator =>>>> App state changed to ', newAppState)
      // getFcmToken()
    }, // 'active' | 'background' | 'inactive' | 'unknown' | 'extension'
  })

  const saveTokenToDatabase = async (token: any) => {
    if (token) {
      LogEvent('save_token_firebase_app_state_change', token)
      // call store
      if (profileStore.isSignedIn()) {
        await profileStore.updateTokenNotification(token)
      }
    }
  }

  // useEffect(() => {
  //   const listener: any = EventRegister.addEventListener(EVENT.allowLocation, (data) => {
  //     __DEV__ && console.log('allowLocation EVENT', data)
  //     showError('', data)
  //   })
  //   const listenerWatchLocation: any = EventRegister.addEventListener(EVENT.watchLocationChange, (data) => {
  //     __DEV__ && console.log('watchLocationChange EVENT', data)
  //     callLocation()
  //   })
  //
  //   return () => {
  //     EventRegister.removeEventListener(listener)
  //     EventRegister.removeEventListener(listenerWatchLocation)
  //
  //     // Clear watch on unmount if watchId exists
  //     if (watchId !== null) {
  //       Geolocation.clearWatch(watchId)
  //     }
  //     try {
  //       Geolocation.stopObserving()
  //     } catch (e) {
  //
  //     }
  //   }
  // }, [watchId])

  const callLocation = () => {
    // Stop observing to prevent duplicate listeners if already observing
    try {
      Geolocation.stopObserving()
    } catch (e) {

    }

    // Start watching the location
    const id = Geolocation.watchPosition(
      async (pos) => {
        __DEV__ && console.log('watchPosition', pos)
        await save('location', {
          latitude: pos.coords.latitude,
          longitude: pos.coords.longitude
        })
      },
      (error) => {
        __DEV__ && console.log(error.message)
      },
      { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
    )

    setWatchId(id)

    // Get the current position
    Geolocation.getCurrentPosition(
      async (pos) => {
        __DEV__ && console.log('Current position', pos)
        await save('location', {
          latitude: pos.coords.latitude,
          longitude: pos.coords.longitude
        })
      },
      (error) => {
        __DEV__ && console.log(error.message)
      },
      { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
    )
  }

  useEffect(() => {
    setTimeout(() => {
      // Get the device token
      getFcmToken()
    }, 1000)

    // If using other push notification providers (ie Amazon SNS, etc)
    // you may need to get the APNs token instead for iOS:
    // if(Platform.OS == 'ios') { messaging().getAPNSToken().then(token => { return saveTokenToDatabase(token); }); }
    messaging().onTokenRefresh(async (fcmToken) => {
      console.log('New FCM Token:', fcmToken)
      saveTokenToDatabase(fcmToken)
    })
  }, [])

  // @ts-ignore
  const getFcmToken = () => {
    messaging().requestPermission().then(r => {
      messaging().getToken().then(token => {
        __DEV__ && console.log('TOKEN FCM root-navigator.tsx', token)
        saveTokenToDatabase(token).then(r => {})

        // Register device to topics
        if (Platform.OS === 'ios') {
          // Register to iOS-specific topic
          messaging().subscribeToTopic('ios').then(r => {})
        } else if (Platform.OS === 'android') {
          // Register to Android-specific topic
          messaging().subscribeToTopic('android').then(r => {})
        }

        // Register to general topic
        messaging().subscribeToTopic('all').then(r => {})
      })
    })
  }

  const goBookingDetail = ({ orderId, typeService }) => {
    // ref?.current.navigation.dispatch(
    //   StackActions.replace(SCREENS.bookingHistoryDetail, { orderId: orderId })
    // )
    if (profileStore.isSignedIn()) {
      // ref?.current?.navigate(SCREENS.notificationScreen)
      // @ts-ignore
      ref?.current?.navigate(SCREENS.settingStack, { screen: SCREENS.bookingHistoryDetail, params: { orderId, bookingType: typeService } })
    } else {
      // @ts-ignore
      ref?.current?.navigate(SCREENS.authStack, { screen: SCREENS.login })
    }
  }

  const resetCountBadge = async () => {
    // Use AppBadge utility to reset badge count
    setTimeout(async () => {
      await AppBadge.resetBadgeCount(profileStore.isSignedIn())
      // Also update the notification store's unread count
      await notificationStore.getUnreadNotificationCount()
    }, 500)
  }

  /** Listen for auth state changes */
  useEffect(() => {
    // Lắng nghe sự kiện SESSION_EXPIRED từ api.ts
    const sessionExpiredListener: any = EventRegister.addEventListener('SESSION_EXPIRED', async () => {
      // Prevent multiple dialogs
      if (isShowingSessionExpiredDialog.current) {
        __DEV__ && console.log('Session expired dialog already showing, skipping duplicate event');
        return;
      }

      isShowingSessionExpiredDialog.current = true;
      __DEV__ && console.log('Received SESSION_EXPIRED event, logging out...')

      // Clear all stores
      profileStore?.clearFields()

      ref?.current?.reset({
        index: 0,
        routes: [{ name: SCREENS.homeStack, params: { screen: SCREENS.home } }]
      })

      //showError dialog
      showWarning(
        '',
        'Phiên đăng nhập của bạn đã hết hiệu lực, vui lòng đăng nhập lại.',
        'Đăng nhập',
        () => {
          ref?.current?.navigate(SCREENS.authStack, { screen: SCREENS.login });
          // Reset the flag after navigation
          isShowingSessionExpiredDialog.current = false;
        }
      )
    })

    return () => {
      // Hủy đăng ký listener khi component unmount
      EventRegister.removeEventListener(sessionExpiredListener);
      isShowingSessionExpiredDialog.current = false;
    }
  }, [authContext])

  const goNotificationScreen = (notification: any) => {
    ref?.current?.navigate(SCREENS.settingStack, {
      screen: SCREENS.notificationScreen,
      params: {
        tabIndex: 1,
        notification: notification
      }
    });
  }

    /**
   * Xử lý điều hướng từ FCM notification với đầy đủ stack, màn hình và dữ liệu
   * @param notification - Object notification từ FCM
   */
  const handleFCMNotificationNavigation = (notification: any) => {
    if (!notification) return;
    
    __DEV__ && console.log('Processing FCM notification:', notification);

    try {
      // Xử lý trường hợp notification có data.notificationDetail
      if (notification.data?.notificationDetail) {
        const notificationDetail = JSON.parse(notification.data.notificationDetail);
        __DEV__ && console.log('Parsed notification detail:', notificationDetail);
        goNotificationScreen(notification)
      } else {
        // Trường hợp mặc định: Điều hướng đến màn hình thông báo
        ref?.current?.navigate(SCREENS.settingStack, {
          screen: SCREENS.notificationScreen,
          params: {
            tabIndex: 1,
            notification: notification
          }
        });
      }
    } catch (error) {
      __DEV__ && console.error('Error handling notification navigation:', error);
      // Fallback: Điều hướng đến màn hình thông báo
    }

  };
  
  useEffect(() => {

    // cố tình hardcode để test open notification
    // goBookingDetail({ orderId: '2021013172215', bookingType: BookingType.SPA }) // go detail spa

    // setTimeout(() => {
    //   ref?.current?.navigate(SCREENS.notificationScreen)
    // }, 500)

    registerAppWithFCM()
    // 1. notification - request permision
    requestUserPermission()
    // 2. Register background handler
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      __DEV__ && console.log('Message handled in the background!', remoteMessage)

      // Update badge count in background
      if (profileStore.isSignedIn()) {
        try {
          // Get the updated unread count from the API
          const unreadCount = await notificationStore.getUnreadNotificationCount()

          // Update the badge count with the new unread count
          await AppBadge.updateBadgeCount(
            unreadCount?.data || 0,
            profileStore.isSignedIn()
          )

          __DEV__ && console.log('Updated badge count in background:', unreadCount?.data || 0)
        } catch (error) {
          __DEV__ && console.error('Error updating badge count in background:', error)
        }
      }
    })

    messaging().onNotificationOpenedApp(notification => {
      __DEV__ && console.log(
        'Notification caused app to open from background state:',
        notification,
      )
      handleFCMNotificationNavigation(notification)
    })

    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(notification => {
        if (notification) {
          console.log('Notification caused app to open from quit state:', notification)
        }
      })

    return messaging().onMessage(async remoteMessage => {
      console.log('A new FCM message arrived!', JSON.stringify(remoteMessage))

      // Update unread notification count when a new notification arrives
      if (profileStore.isSignedIn()) {
        try {
          // Get the updated unread count from the API
          const unreadCount = await notificationStore.getUnreadNotificationCount()

          // Update the badge count with the new unread count
          await AppBadge.updateBadgeCount(
            unreadCount?.data || 0,
            profileStore.isSignedIn()
          )

          __DEV__ && console.log('Updated badge count after new FCM message:', unreadCount?.data || 0)
        } catch (error) {
          __DEV__ && console.error('Error updating badge count:', error)
        }
      }
    })
  }, [])

  return (
    <NavigationContainer {...props} ref={ref}>
      <RootStack {...props}/>
    </NavigationContainer>
  )
})

RootNavigator.displayName = 'RootNavigator'
