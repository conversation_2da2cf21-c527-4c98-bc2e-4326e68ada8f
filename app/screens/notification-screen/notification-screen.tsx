import { Button<PERSON><PERSON>, Lazy<PERSON>mage, PlaceHolder, TButton } from '@app/components'
import { useStores } from '@app/models'
import { useNavigation } from '@react-navigation/native'
import { observer } from 'mobx-react-lite'
import moment from 'moment'
import React, { useEffect, useRef, useState } from 'react'
import {
  Dimensions,
  Linking,
  ScrollView,
  Text,
  useWindowDimensions,
  View,
} from 'react-native'
import styles from './styles'

import { SCREENS } from '@app/navigation'
import { color } from '@app/theme'
import common, { linearGradientProps } from '@app/theme/styles/common'
import { isAndroid } from '@app/utils/chat/deviceInfo'
import { useTranslation } from 'react-i18next'
import { Header } from 'react-native-elements'
import LinearGradient from 'react-native-linear-gradient'
import { Modalize } from 'react-native-modalize'
import { APP_STORE_LINK, PLAY_MARKET_LINK } from '@app/constants/links'
import { NotificationType } from '@app/models/constants/constants'
import { palette } from '@app/theme/palette'
import { convert } from 'html-to-text'
import RenderHtml from 'react-native-render-html'
import { TabBar, TabView } from 'react-native-tab-view'
import validate from 'validate.js'
import { TabNotiRender } from './components/tab-noti-render-screen'
import { TabAllRender } from './components/tab-all-render-screen'
import { NotificationItem } from '@app/models/notification-store/notification-interface.ts';
import { TabHistoryRender } from '@app/screens/notification-screen/components/tab-history-render-screen.tsx';
import { AppBadge } from '@app/utils/appBadge';

const initialLayout = { width: Dimensions.get('window').width }
export const NotificationScreen: React.FC = observer((props) => {
  // Get route params from navigation
  const { route } = props as any;
  console.log('NotificationScreen')
  const { t }: any = useTranslation()
  const { navigate } = useNavigation<any>()
  const modalizeRef = useRef<Modalize>(null)
  const navigation = useNavigation<any>()
  const goBack = () => navigation.goBack()
  const [itemData, setItemData] = useState<NotificationItem>()
  const [index, setIndex] = useState(route?.params?.tabIndex !== undefined ? route.params.tabIndex : 0) // Set default tab index from params or 0
  const { notificationStore, profileStore } = useStores()
  const { width } = useWindowDimensions()

  const [routes] = React.useState([
    { key: 'HISTORY', title: 'Lịch sử giao dịch' },
    { key: 'NOTIFI', title: 'Thông báo' }
  ])

  useEffect(() => {
    // Fetch unread notification count when screen loads
    console.log('getUnreadNotificationCount 2');
    const updateNotificationCount = async () => {
      try {
        // Get the updated unread count from the API
        const unreadCount = await notificationStore.getUnreadNotificationCount()

        // Reset badge count when notification screen is opened
        await AppBadge.resetBadgeCount(profileStore.isSignedIn())

        __DEV__ && console.log('Reset badge count on notification screen open')
      } catch (error) {
        __DEV__ && console.error('Error updating notification count:', error)
      }
    }

    updateNotificationCount()
    
    // Process notification from params if available
    if (route?.params?.notification) {
      const notificationData = route.params.notification;
      __DEV__ && console.log('Processing notification from params:', notificationData);
      
      // Handle notification from FCM
      if (notificationData.data?.notificationDetail) {
        try {
          const parsedDetail = JSON.parse(notificationData.data.notificationDetail);
          const notificationItem: NotificationItem = {
            id: parsedDetail.id,
            title: parsedDetail.title,
            body: parsedDetail.body,
            event: parsedDetail.event,
            extraData: parsedDetail.extra,
            isRead: false,
            sentAt: 0,
            platform: '',
            status: '',
            createdBy: '',
            createdAt: 0,
            updatedBy: '',
            updatedAt: 0
          };
          
          // Call checkAndOpenPage with parsed notification
          setTimeout(() => {
            checkAndOpenPage(notificationItem);
          }, 300);
        } catch (error) {
          __DEV__ && console.error('Error parsing notification detail:', error);
        }
      } else if (typeof notificationData === 'object') {
        // Handle notification object passed directly
        setTimeout(() => {
          checkAndOpenPage(notificationData);
        }, 300);
      }
    }
  }, [])


  const onOpenModal = () => {
    modalizeRef.current?.open()
  }

  const onCloseModal = () => {
    // onRefresh()
    modalizeRef.current?.close()
  }


  const checkAndOpenPage = async (item: NotificationItem) => {
    console.log('checkAndOpenPage', item);
    // Mark notification as read when touched    
    setItemData(item)
    switch (item?.event) {
      case 'ORDER':
        // navigate(SCREENS.bookingHistoryDetail, { orderId: item.orderId, bookingType: typeOrderProduct }) // product
        break
      default:
        onOpenModal()
        break
    }
    if (!item?.isRead) {
      // Call the API to mark as read
      await notificationStore.watchedNotification(item.id)
    
      // Update badge count after marking notification as read
      try {
        // Get the updated unread count from the API
        const unreadCount = await notificationStore.getUnreadNotificationCount()

        // Update the badge count with the new unread count
        await AppBadge.updateBadgeCount(
          unreadCount?.data || 0,
          profileStore.isSignedIn()
        )

        __DEV__ && console.log('Updated badge count after marking notification as read:', unreadCount?.data || 0)
      } catch (error) {
        __DEV__ && console.error('Error updating badge count:', error)
      }
    }
  }

  const renderHeaderModalize: any = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModal} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>Chi tiết thông báo</Text>
      <View style={{ width: 20 }} />
    </View>
  )

  const updateNow = () => {
    const link = isAndroid ? PLAY_MARKET_LINK : APP_STORE_LINK
    Linking.canOpenURL(link).then(
      (supported) => {
        supported && Linking.openURL(link)
      },
      (err) => console.log(err)
    )
  }

  function tryParseJSON(jsonString: string) {
    try {
      return JSON.parse(jsonString)
    } catch (e) {
      __DEV__ && console.warn('Invalid JSON:', e)
      return null // hoặc bạn có thể trả về một giá trị mặc định khác nếu cần
    }
  }

  function viewNow(itemNotification: any) {
    const item = tryParseJSON(itemNotification?.extraData)
    if (!item) {
      __DEV__ && console.warn('Could not parse extraData:', itemNotification?.extraData)
      return // Thoát ra nếu item không hợp lệ
    }

    console.log('onPress =>>>>>>>>>>>>>>>>>', item)
    if (item?.screen_open) {
      const params = item?.screen_param || ''
      const trueParams = params !== '#'

      switch (item.screen_open) {
        case 'PROMOTION_DETAIL_SCREEN':
          if (trueParams) {
            navigate(item.screen_open, { id: params })
          }
          break
        case 'SHOPPING_STACK':
          navigate(item.screen_open)
          break
        case 'PRODUCT_DETAILS':
          if (trueParams) {
            navigate(item.screen_open, { id: params })
          }
          break
        case 'BLOG_DETAIL_SCREEN':
          if (trueParams) {
            navigate(item.screen_open, { id: params })
          }
          break
        case 'SERVICE_DETAIL':
          if (trueParams) {
            navigate(item.screen_open, { id: params })
          }
          break
        case 'POPUP_SERVICE_OF_BRAND':
          if (params && item?.screen_param_spaId) {
            navigate(SCREENS.serviceDetail, { id: params, spaId: item?.screen_param_spaId })
          }
          break
        case 'BLOG_SCREEN':
          if (trueParams) {
            navigate(item.screen_open)
          }
          break
        case 'BHTNDSCAR':
          if (trueParams) {
            navigate(SCREENS.baoHiemTNDSB1, { prodId: params })
          }
          break
        case 'BHVCCAR':
          if (trueParams) {
            navigate(SCREENS.baoHiemVCXB1, { prodId: params })
          }
          break
        case 'BHTNDSBIKE':
          if (trueParams) {
            navigate(SCREENS.baoHiemTNDSXeMayB1, { prodId: params })
          }
          break
        case 'SCREEN_NAME':
          navigate(item?.screen_param || '')
          break
        default:
          const isValidURL = validate({ website: params }, { website: { url: true } })
          if (trueParams && isValidURL === undefined) {
            Linking.openURL(params)
          }
          break
      }
    }
  }

  const renderDetail = () => {
    const itemJson: NotificationItem = tryParseJSON(itemData?.extraData || '{}')
    __DEV__ && console.log('itemData?.extraData', itemJson)
    return (
      <ScrollView style={styles.viewDetailNoti}>
        <Text style={styles.textTitleDetailNoti}>{itemData?.title}</Text>
        {/* <Text style={styles.admin}>{itemData.message}</Text> */}
        <RenderHtml
          contentWidth={width}
          source={{ html: itemData?.body || ''}}
          renderersProps={{
            img: {
              enableExperimentalPercentWidth: true
            }
          }}
          ignoredDomTags={['script']}
          ignoredStyles={['fontFamily']}
        />
        <Text style={styles.admin}></Text>
        {itemData?.type == NotificationType.AdminSendNotification && itemData?.typeNotify == 2 ? <TButton typeRadius={'rounded'} buttonStyle={{ backgroundColor: '#0A5936', marginTop: 20 }} title={t('Cập nhật ngay')} onPress={updateNow} /> : null}
        {itemData?.extraData ? <TButton typeRadius={'rounded'} buttonStyle={{ backgroundColor: '#0A5936', marginTop: 20 }} title={t(itemJson?.buttonName || 'Xem ngay')} onPress={() => viewNow(itemData)} /> : null}
        {/* <Text style={styles.textTime}>{moment(itemData.createAt).locale('vi').fromNow()}</Text> */}
      </ScrollView>

    )
  }



  const renderScene = ({ route }) => {
    __DEV__ && console.log('renderScene INDEX =>>>>>>>', index)
    switch (route.key) {
      // case 'SHOP':
      // return <TabProductRenderScreen data={productStore.productNears}/>
      case 'HISTORY':
        return <TabHistoryRender type={'history'} checkAndOpenPage={checkAndOpenPage} />
      case 'NOTIFI':
        return <TabNotiRender type={'notifi'} checkAndOpenPage={checkAndOpenPage} />
      case 'ALL':
        return <TabAllRender type={'all'} checkAndOpenPage={checkAndOpenPage} />
      default:
        return null
    }
  }

  const renderTabBar = (props) => {
    // Tách key ra khỏi props
    const { key, ...restProps } = props;

    return (
      <TabBar
        {...restProps}
        indicatorStyle={{ backgroundColor: color.palette.primaryGreen }} // Thay green thành primaryGreen vì không có color.palette.green
        style={styles.tabBar}
        renderLabel={({ route, focused, color }) => (
          <Text style={[styles.tabBarText, { color: focused ? palette.primaryGreen : '#848484' }]}>
            {route.title}
          </Text>
        )}
      />
    );
  };


  return (<View style={styles.safeAreaView}>
    {/* <ButtonBack onPress={goBack} style={styles.icArrowBack}/> */}
    {/* <Text style={styles.textTitleTotal}>Tất cả thông báo</Text> */}
    <Header
      // statusBarProps={{ barStyle: 'light-content' }}
      // barStyle="light-content" // or directly
      leftComponent={<ButtonBack style={common.buttonBack} onPress={goBack} />}
      centerComponent={{ text: t(t('Thông báo')), style: common.headerCenterTitle }}
      // rightComponent={bookingData && bookingData.status === 0 ? <TouchableOpacity onPress={() => props.onOpenCancelModal()}>
      //   <Text style={styles.viewBtnTop}>Hủy đơn</Text>
      // </TouchableOpacity> : null}
      containerStyle={common.headerContainer}
      statusBarProps={{ barStyle: 'dark-content' }}
      // ViewComponent={LinearGradient}
      // //linearGradientProps={linearGradientProps}
    />
    <TabView
      navigationState={{ index, routes }}
      renderScene={renderScene}
      onIndexChange={setIndex}
      initialLayout={initialLayout}
      renderTabBar={renderTabBar}
    // style={{ backgroundColor: '#fff' }}
    />
    {/* {isFetched ? <PlaceHolder/> : <View style={styles.viewFlatlist}>{!data || !data.length
      ? <EmptyData title={'Không có thông báo'} message={'Thông báo sẽ hiển thị khi có tin nhắn từ hệ thống'}/>
      : <FlatList
        showsVerticalScrollIndicator={false}
        data={data}
        initialNumToRender={10}
        refreshing={refreshing}
        onRefresh={onRefresh}
        keyExtractor={(item, index) => item._id + index }
        renderItem={renderItem}
        extraData={data}
        onScrollBeginDrag={e => {
          __DEV__ && console.log('onScrollBeginDrag')
          setLoadMore(true)
        }}
        onMomentumScrollEnd={handleLoadMore}
        ListFooterComponent={renderFooter}
      />}</View>
    } */}
    <Modalize
      HeaderComponent={renderHeaderModalize}
      ref={modalizeRef}
      adjustToContentHeight
      keyboardAvoidingBehavior={'padding'}
    >
      {itemData ? renderDetail() : null}
    </Modalize>
  </View>)
})
