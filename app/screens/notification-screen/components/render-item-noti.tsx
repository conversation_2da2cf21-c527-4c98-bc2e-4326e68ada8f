import React, { useImperative<PERSON>andle } from 'react'
import { observer } from 'mobx-react-lite'
import {
  View, Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native'

import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import moment from 'moment'
import { color, spacing, typography } from '@app/theme';
import { ifIphoneX } from 'react-native-iphone-x-helper'
import { numberFormat } from '@app/utils'
import { SCREENS } from '@app/navigation'
import { useStores } from '@app/models'
import FastImage from 'react-native-fast-image'
import { icNoti } from '@app/assets/images'
import { convert } from 'html-to-text';
import { NotificationItem } from '@app/models/notification-store/notification-interface.ts';
import { AppBadge } from '@app/utils/appBadge';

/**
 * Describe your component here
 */

interface Props {
  data?: any,
  item: NotificationItem,
  index?: any,
  checkAndOpenPage?: (value) => void
  type?: string
  source?: string
}

const optionConvertHtml = {
  wordwrap: 130, // Giới hạn độ dài dòng
  preserveNewlines: true, // Bảo toàn ký tự xuống dòng
  selectors: [
    { selector: 'ol', format: 'inline' },
    { selector: 'li', format: 'inline', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
    { selector: 'br', format: 'inline', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
    { selector: 'strong', format: 'inline' }
  ]
}

export const RenderItemNotificationComponent = observer((props: Props) => {
  const { item, index, checkAndOpenPage, type, source } = props
  // Use markAsRead if provided, otherwise fall back to daXemThongBao

  const { navigate } = useNavigation<any>()
  const { notificationStore, profileStore } = useStores()


  // const [distance, setDistance] = useState(20)
  // const [filter, setFilter] = useState(null)
  const { t }: any = useTranslation()

  const RenderTransactionHistoryItem = () => {
    return (
      <View style={{ backgroundColor: item?.isRead ? '#fff' : '#ffffff', borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 10, marginHorizontal: 16, marginTop: 16 }}>
        <View style={styles.container}>
          <FastImage source={icNoti} style={styles.viewImage} />
          <TouchableOpacity  onPress={() => {
        // checkAndOpenPage(item)
        // if (item.isRead === 0) {
        //   daXemThongBao && daXemThongBao(item, index)
        // }
        navigate(SCREENS.tranSactionDetail, { resultData: item })
      }} style={styles.containerComment}>
            <Text style={styles.textTime}>{moment(item.createdAt).format('DD/MM/YYYY HH:mm:ss')}</Text>
            <View>
              <View style={{ flexDirection: 'row', paddingBottom: 5 }}>
                <Text style={styles.orderId}>#{item.id} {item.transTypeInfo?.transName}<Text style={{ color: '#78AF41' }}> {numberFormat(item.advanceAmount) + "đ"}</Text> Tài khoản nhận: {item.destinationAccount} {item.destinationBankName}  .Tài khoản chuyển: {item.sourceAccount} {item.sourceBankName} thực hiện lúc {moment(item.createdAt).format('DD/MM/YYYY HH:mm:ss')}</Text>
              </View>
            </View>
          </TouchableOpacity>
          {/*{!item.isRead ? <View style={styles.dot} /> : null}*/}
        </View>
        <View style={styles.containerFooter}>
          <TouchableOpacity style={styles.btnTraSoat} onPress={() => {
            // Với cấu trúc stack mới, không cần phân biệt nguồn điều hướng
            navigate(SCREENS.transactionHistoryStack, {
              screen: SCREENS.yeuCauTraSoatScreen,
              params: {
                selectedItem: item
              }
            })
          }} >
            <Text style={{ color: '#78AF41' }}>Tra soát</Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }
  const RenderItemNotification = () => {
    const handlePress = async () => {
      try {
        // Handle navigation when a notification is tapped
        if (checkAndOpenPage) {
          checkAndOpenPage(item)
        }      

      } catch (error) {
        __DEV__ && console.log('Error marking notification as read:', error)
      }
    }

    return (
      <TouchableOpacity
        style={{borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 10, marginHorizontal: 16, marginTop: 8, backgroundColor: '#fff' }}
        onPress={handlePress}
      >
        <View style={[styles.container]}>
          <FastImage source={icNoti} style={styles.viewImage} />
          <View style={styles.containerComment}>
            <Text style={styles.textTime}>{moment(item.sentAt).format('DD/MM/YYYY')}</Text>
            <View >
              <Text style={styles.textTitle}>{item.title}</Text>
              <View>
                <View style={{ flexDirection: 'row', paddingBottom: 5 }}>
                  <Text numberOfLines={2} style={styles.orderId}>{convert(item.body, optionConvertHtml)}</Text>
                </View>
              </View>
            </View>
          </View>
          {!item.isRead ? <View style={styles.dot} /> : null}
        </View>
      </TouchableOpacity>
    )
  }

  return (
    <View>
      {type === 'history' && <RenderTransactionHistoryItem />}
      {type === 'notifi' && <RenderItemNotification />}
    </View>
  )
})

const styles = StyleSheet.create({

  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    paddingVertical: 16
  },
  containerComment: {
    flex: 1,
    marginLeft: 8,
    marginRight: 16
    // flexDirection: "column"
  },

  dot: {
    backgroundColor: color.primary,
    borderRadius: 8,
    bottom: 12,
    elevation: 2,
    height: 8,
    left: -10,
    position: 'absolute',
    top: 8,
    shadowColor: 'rgba(0, 0, 0, 0.15)',
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: 1,
    shadowRadius: 5,
    width: 8
  },

  imageBooking: {
    borderRadius: 5,
    height: 44,
    width: 44,
  },
  imageUser: {
    borderRadius: 22,
    height: 44,
    width: 44,
  },

  orderId: {
    fontSize: 12,
    color: '#2D384C',
    lineHeight: 18
  },

  textContentNoti: {
    color: '#616161',
    fontSize: 14,
  },
  textTime: {
    color: '#9D9D9D',
    fontFamily: typography.normal,
    fontSize: 12,
    letterSpacing: 0,
    marginBottom: spacing.tiny
  },
  textTitle: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 12,
  },
  textTitleDetailNoti: {
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 14,
    marginBottom: 10
  },
  textTitleHeader: {
    alignItems: 'center',
    color: '#333',
    flex: 1,
    fontFamily: typography.normal,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',

  },
  textTitleTotal: {
    color: '#333',
    fontFamily: typography.normal,
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: spacing.small,
  },
  viewContent: {
    flexDirection: 'row',
    paddingVertical: 5
  },
  viewContentSystem: {
    marginBottom: 5
  },
  viewDetailNoti: {
    marginBottom: 30,
    marginLeft: 15,
    marginRight: 15,
    marginTop: 10,
    ...ifIphoneX({
      paddingBottom: 120,
    }, {
      paddingBottom: 90,
    }),
  },
  viewFlatlist: {
    flex: 1,
  },
  viewIcon: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
  },
  viewImage: {
    height: 50,
    width: 50,
    marginRight: 4,
    borderRadius: 50,
    backgroundColor: '#F3F3F3'
  },
  viewService: {
    flexDirection: 'column',
    marginTop: 17
  },
  viewStar: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between'
  },
  viewTouchButtonTop: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  tabBar: {
    backgroundColor: '#fff',
    borderTopColor: 'transparent',
    borderTopWidth: 0,
    elevation: 0,
    shadowColor: '#5bc4ff',
    shadowOffset: {
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  tabBarText: {
    color: '#acb1c0',
    fontFamily: typography.normal,
    fontSize: 14,
  },
  containerFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: 16,
    marginRight: 16,
  },
  btnTraSoat: {
  }
})
