import {
  Text,
  View,
  ScrollView,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform
} from 'react-native'
import React, { useState, useEffect, useContext } from 'react'
import { useTranslation } from 'react-i18next'
import { useStores } from '../../models/root-store'
import { CommonActions, useNavigation, useRoute } from '@react-navigation/native'
import { observer } from 'mobx-react-lite'
import { ButtonBack, TTextInput, TButton } from '@app/components'
import { color } from '@app/theme'
import { Api } from '@app/services/api'
import { ModalContext } from '@app/context'
import Icon from 'react-native-vector-icons/Ionicons'
import { PasswordInput } from '@app/components/password-input.tsx'
import { SCREENS } from '@app/navigation'
import styles from './styles'
import { ResetPasscodeWithOtpRequest } from '@app/services/api/interface/passcode.interface'


export const ChangePasscodeViaOtpScreen = observer(function ChangePasscodeViaOtpScreen() {
  const { t } = useTranslation()
  const navigation = useNavigation<any>()
  const route = useRoute<any>()
  const { accountStore, profileStore } = useStores()
  const [newPasscode, setNewPasscode] = useState('')
  const [confirmPasscode, setConfirmPasscode] = useState('')
  const [loading, setLoading] = useState(false)
  const { showError, showSuccess, showCustomSuccess, showModal, showCustomError } = useContext(ModalContext)

  // Get phone number and OTP code from route params (passed from confirmCode screen)
  const { phoneNumber, code, otpType } = route?.params || {}

  const goBack = () => navigation.goBack()
  const onBack = () => navigation.goBack()

  // Check if user is logged in
  useEffect(() => {
    if (!accountStore.isLogin()) {
      showError('', t('Vui lòng đăng nhập để thay đổi mã passcode'))
      navigation.navigate(SCREENS.login)
    }
  }, [])

  const validateInputs = () => {
    if (!code) {
      showError('', t('Mã OTP không hợp lệ'))
      return false
    }
    if (!newPasscode) {
      showError('', t('Vui lòng nhập mã passcode mới'))
      return false
    }
    if (newPasscode.length < 6) {
      showError('', t('Mã passcode phải đủ 6 ký tự'))
      return false
    }
    if (newPasscode !== confirmPasscode) {
      showError('', t('Mã passcode và xác nhận không khớp'))
      return false
    }
    return true
  }

  const navigateToProfile = () => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: SCREENS.settingStack,
            state: {
              routes: [{ name: SCREENS.profileUser }],
            },
          },
        ],
      })
    )
  }

  const onSubmit = async () => {
    if (!validateInputs()) return

    try {
      setLoading(true)
      const requestData: ResetPasscodeWithOtpRequest = {
        otp: code,
        newPasscode,
        confirmPasscode,
        phoneNumber: phoneNumber
      }

      const result = await profileStore.resetPasscodeWithOtp(requestData)
      
      if (result.kind === 'ok' && result.data?.success) {
        showSuccess('', t('Thay đổi mã passcode thành công'))
        // Navigate back to profile or passcode settings
        navigateToProfile()
      } else {
        const errorMessage = result.data?.data?.message || result.data?.message || t('Có lỗi xảy ra, vui lòng thử lại sau')
        showError('', errorMessage)
      }
    } catch (error) {
      console.log('Reset passcode with OTP error:', error)
      showError('', t('Có lỗi xảy ra, vui lòng thử lại sau'))
    } finally {
      setLoading(false)
    }
  }

  // If user is not logged in, don't render the UI
  if (!accountStore.isLogin()) {
    return null
  }

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <ButtonBack onPress={onBack} style={styles.icArrowBack} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView
          keyboardShouldPersistTaps="handled"
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            <View>
              <Text style={styles.textTitle}>
                {t('Thay đổi mã passcode với OTP')}
              </Text>
              <Text style={styles.description}>
                {t('Mã OTP đã được xác thực thành công. Vui lòng nhập mã passcode mới.')}
              </Text>

              <View style={styles.otpConfirmedContainer}>
                <View>
                  {/* @ts-ignore */}
                  <Icon name="checkmark-circle" size={20} color="#4CAF50" style={{ marginRight: 8 }} />
                </View>
                <Text style={styles.otpConfirmedText}>
                  {t('Mã OTP đã được xác thực')}
                </Text>
              </View>
            </View>

            <View style={styles.mainTextInput}>
              <View style={styles.formGroup}>
                <Text style={styles.label}>{t('Mã passcode mới')}</Text>
                <PasswordInput
                  value={newPasscode}
                  onChange={setNewPasscode}
                  placeholder={t('Nhập mã passcode mới')}
                  style={styles.input}
                  keyboardType="numeric"
                  maxLength={6}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>{t('Xác nhận mã passcode mới')}</Text>
                <PasswordInput
                  value={confirmPasscode}
                  onChange={setConfirmPasscode}
                  placeholder={t('Nhập lại mã passcode mới')}
                  style={styles.input}
                  keyboardType="numeric"
                  maxLength={6}
                />
              </View>

              <View style={styles.noteContainer}>
                <View>
                  {/* @ts-ignore */}
                  <Icon name="information-circle-outline" size={20} color={color.primary} style={{ marginRight: 5 }} />
                </View>
                <Text style={styles.noteText}>
                  {t('Mã passcode phải đủ 6 ký tự')}
                </Text>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <TButton
                buttonStyle={styles.button}
                title={t('Xác nhận')}
                onPress={onSubmit}
                loading={loading}
                typeRadius={'rounded'}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
})
