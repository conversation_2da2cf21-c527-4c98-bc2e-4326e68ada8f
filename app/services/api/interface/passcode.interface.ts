import { GeneralApiProblem } from '../api-problem';

// Passcode Info Interface
export interface PasscodeInfo {
  userId: string;
  passcodeExpiryDate: string;
  passcodeFailedAttempts: number;
  passcodeLastResetDate: string;
  passcodeEnabled: boolean;
  locked: boolean;
  expired: boolean;
}

// Reset Passcode with OTP Types
export interface ResetPasscodeWithOtpRequest {
  phoneNumber: string;
  otp: string;
  newPasscode: string;
  confirmPasscode: string;
}

export interface ResetPasscodeWithOtpResponseData {
  status: string;
  message: string;
  success: boolean;
}

export interface ResetPasscodeWithOtpResponse {
  statusCode: null;
  httpStatusCode: number;
  description: null;
  clientMessageId: string;
  timestamp: number;
  data: ResetPasscodeWithOtpResponseData;
}

export type ResetPasscodeWithOtpResult =
  | { kind: "ok" } & ResetPasscodeWithOtpResponse
  | GeneralApiProblem
